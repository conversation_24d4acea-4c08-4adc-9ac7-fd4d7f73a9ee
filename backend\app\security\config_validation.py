"""
Configuration validation and secure secret management.

This module provides utilities for validating configuration on startup,
detecting hardcoded secrets, and implementing secure secret management.
"""

import os
import logging
import re
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class ConfigurationIssue:
    """Configuration validation issue."""
    severity: str  # 'critical', 'high', 'medium', 'low'
    category: str  # 'security', 'performance', 'compliance'
    message: str
    field: str
    recommendation: str


class ConfigurationValidator:
    """Validates configuration for security and compliance."""
    
    def __init__(self):
        self.issues: List[ConfigurationIssue] = []
        
        # Patterns that indicate hardcoded secrets
        self.secret_patterns = [
            r'your-.*-key',
            r'development-only',
            r'test-secret',
            r'default-.*',
            r'changeme',
            r'password123',
            r'secret123',
            r'key123',
            r'admin.*password',
            r'root.*password'
        ]
        
        # Required environment variables for production
        self.required_production_vars = {
            'JWT_SECRET_KEY',
            'DATABASE_URL',
            'REDIS_URL',
            'ENVIRONMENT'
        }
        
        # Optional but recommended environment variables
        self.recommended_vars = {
            'WEBHOOK_SECRET_KEY',
            'SMTP_PASSWORD',
            'GOOGLE_CLIENT_SECRET',
            'ENCRYPTION_KEY'
        }
        
        # Environment variables that should not be empty in production
        self.non_empty_production_vars = {
            'JWT_SECRET_KEY',
            'DATABASE_URL'
        }
    
    def _add_issue(self, severity: str, category: str, message: str, 
                   field: str, recommendation: str):
        """Add a configuration issue."""
        issue = ConfigurationIssue(
            severity=severity,
            category=category,
            message=message,
            field=field,
            recommendation=recommendation
        )
        self.issues.append(issue)
        
        # Log the issue
        log_level = {
            'critical': logging.CRITICAL,
            'high': logging.ERROR,
            'medium': logging.WARNING,
            'low': logging.INFO
        }.get(severity, logging.INFO)
        
        logger.log(log_level, f"Config {severity.upper()}: {message} ({field})")
    
    def _check_hardcoded_secrets(self, env_vars: Dict[str, str]):
        """Check for hardcoded secrets in environment variables."""
        for var_name, var_value in env_vars.items():
            if not var_value:
                continue
            
            # Check if variable name suggests it's a secret
            secret_indicators = ['secret', 'key', 'password', 'token', 'auth']
            is_secret_var = any(indicator in var_name.lower() for indicator in secret_indicators)
            
            if is_secret_var:
                # Check for weak patterns
                for pattern in self.secret_patterns:
                    if re.search(pattern, var_value.lower()):
                        self._add_issue(
                            'critical',
                            'security',
                            f'Hardcoded or weak secret detected in {var_name}',
                            var_name,
                            f'Replace {var_name} with a strong, unique secret'
                        )
                        break
                
                # Check minimum length for secrets
                if len(var_value) < 16:
                    self._add_issue(
                        'high',
                        'security',
                        f'Secret {var_name} is too short (minimum 16 characters)',
                        var_name,
                        f'Use a longer secret for {var_name}'
                    )
    
    def _check_required_variables(self, env_vars: Dict[str, str], environment: str):
        """Check for required environment variables."""
        if environment == 'production':
            for var_name in self.required_production_vars:
                if var_name not in env_vars or not env_vars[var_name]:
                    self._add_issue(
                        'critical',
                        'security',
                        f'Required production variable {var_name} is missing or empty',
                        var_name,
                        f'Set {var_name} environment variable'
                    )
        
        # Check non-empty requirements
        for var_name in self.non_empty_production_vars:
            if var_name in env_vars and not env_vars[var_name].strip():
                self._add_issue(
                    'critical',
                    'security',
                    f'Variable {var_name} cannot be empty',
                    var_name,
                    f'Provide a value for {var_name}'
                )
    
    def _check_security_settings(self, env_vars: Dict[str, str], environment: str):
        """Check security-related settings."""
        if environment == 'production':
            # HTTPS should be enforced in production
            if env_vars.get('ENFORCE_HTTPS', 'false').lower() != 'true':
                self._add_issue(
                    'high',
                    'security',
                    'HTTPS not enforced in production',
                    'ENFORCE_HTTPS',
                    'Set ENFORCE_HTTPS=true in production'
                )
            
            # Rate limiting should be enabled in production
            if env_vars.get('RATE_LIMITING_ENABLED', 'true').lower() != 'true':
                self._add_issue(
                    'medium',
                    'security',
                    'Rate limiting disabled in production',
                    'RATE_LIMITING_ENABLED',
                    'Set RATE_LIMITING_ENABLED=true in production'
                )
        
        # Check for debug mode in production
        if environment == 'production':
            debug_vars = ['DEBUG', 'FLASK_DEBUG', 'DJANGO_DEBUG']
            for debug_var in debug_vars:
                if env_vars.get(debug_var, 'false').lower() == 'true':
                    self._add_issue(
                        'high',
                        'security',
                        f'Debug mode enabled in production ({debug_var})',
                        debug_var,
                        f'Set {debug_var}=false in production'
                    )
    
    def _check_database_security(self, env_vars: Dict[str, str]):
        """Check database security configuration."""
        db_url = env_vars.get('DATABASE_URL', '')
        
        if db_url:
            # Check for insecure database connections
            if db_url.startswith('postgresql://') and 'sslmode=require' not in db_url:
                self._add_issue(
                    'medium',
                    'security',
                    'Database connection does not require SSL',
                    'DATABASE_URL',
                    'Add sslmode=require to DATABASE_URL for secure connections'
                )
            
            # Check for default database credentials
            if 'password=password' in db_url.lower() or 'password=admin' in db_url.lower():
                self._add_issue(
                    'critical',
                    'security',
                    'Default database password detected',
                    'DATABASE_URL',
                    'Use a strong, unique database password'
                )
    
    def validate_configuration(self) -> List[ConfigurationIssue]:
        """Validate current configuration and return issues."""
        self.issues = []  # Reset issues
        
        # Get environment variables
        env_vars = dict(os.environ)
        environment = env_vars.get('ENVIRONMENT', 'development')
        
        logger.info(f"Validating configuration for environment: {environment}")
        
        # Run validation checks
        self._check_hardcoded_secrets(env_vars)
        self._check_required_variables(env_vars, environment)
        self._check_security_settings(env_vars, environment)
        self._check_database_security(env_vars)
        
        # Summary
        critical_count = sum(1 for issue in self.issues if issue.severity == 'critical')
        high_count = sum(1 for issue in self.issues if issue.severity == 'high')
        
        logger.info(f"Configuration validation complete: {len(self.issues)} issues found "
                   f"({critical_count} critical, {high_count} high)")
        
        return self.issues
    
    def get_security_score(self) -> float:
        """Calculate security score based on issues."""
        if not self.issues:
            return 100.0
        
        # Deduct points based on severity
        score = 100.0
        for issue in self.issues:
            if issue.severity == 'critical':
                score -= 25
            elif issue.severity == 'high':
                score -= 15
            elif issue.severity == 'medium':
                score -= 10
            elif issue.severity == 'low':
                score -= 5
        
        return max(0.0, score)
    
    def has_critical_issues(self) -> bool:
        """Check if there are any critical configuration issues."""
        return any(issue.severity == 'critical' for issue in self.issues)


def validate_startup_configuration() -> bool:
    """Validate configuration on application startup."""
    validator = ConfigurationValidator()
    issues = validator.validate_configuration()
    
    if validator.has_critical_issues():
        logger.critical("Critical configuration issues detected. Application startup aborted.")
        
        # Print critical issues
        for issue in issues:
            if issue.severity == 'critical':
                logger.critical(f"CRITICAL: {issue.message} - {issue.recommendation}")
        
        return False
    
    # Log security score
    security_score = validator.get_security_score()
    logger.info(f"Configuration security score: {security_score:.1f}/100")
    
    if security_score < 70:
        logger.warning("Configuration security score is below recommended threshold (70)")
    
    return True


def get_configuration_report() -> Dict[str, Any]:
    """Get comprehensive configuration validation report."""
    validator = ConfigurationValidator()
    issues = validator.validate_configuration()
    
    # Group issues by category and severity
    by_category = {}
    by_severity = {}
    
    for issue in issues:
        if issue.category not in by_category:
            by_category[issue.category] = []
        by_category[issue.category].append(issue)
        
        if issue.severity not in by_severity:
            by_severity[issue.severity] = []
        by_severity[issue.severity].append(issue)
    
    return {
        "timestamp": datetime.now().isoformat(),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "security_score": validator.get_security_score(),
        "total_issues": len(issues),
        "has_critical_issues": validator.has_critical_issues(),
        "issues_by_category": {k: len(v) for k, v in by_category.items()},
        "issues_by_severity": {k: len(v) for k, v in by_severity.items()},
        "issues": [
            {
                "severity": issue.severity,
                "category": issue.category,
                "message": issue.message,
                "field": issue.field,
                "recommendation": issue.recommendation
            }
            for issue in issues
        ]
    }
