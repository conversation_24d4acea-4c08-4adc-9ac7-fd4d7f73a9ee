"""
Performance optimization system for Datagenius - Phase 1 Implementation.

This module provides comprehensive performance optimization including
multi-level caching strategies, database query optimization, memory management,
and response time optimization as outlined in dashboard.md Phase 1.

Target Metrics:
- Dashboard load time: < 1.5 seconds (from ~2.5s)
- Cache hit ratio: > 85%
- Memory usage: < 80MB per session
- WebSocket reconnection: < 2 seconds
"""

import logging
import asyncio
import time
import weakref
import json
import hashlib
from typing import Dict, Any, Optional, List, Callable, Union, Tuple
from datetime import datetime, timedelta
from functools import wraps, lru_cache
from contextlib import asynccontextmanager
from collections import OrderedDict
import threading

import redis
import pickle
from sqlalchemy.orm import sessionmaker
from ..security.sql_security import execute_secure_sql
from sqlalchemy import text, Index, event
from sqlalchemy.engine import Engine

from ..database import engine
from ..config import REDIS_URL
from ..monitoring.metrics import metrics

logger = logging.getLogger(__name__)


class AdvancedCacheManager:
    """
    Multi-level caching system implementing Phase 1 caching strategy:
    L1: Memory cache (fastest, limited size)
    L2: Redis cache (fast, persistent)
    L3: Database query cache (computed results)
    L4: CDN cache (static assets - handled by frontend)

    Features:
    - Intelligent cache warming
    - Dependency-based invalidation
    - Performance metrics tracking
    - Memory usage optimization
    """

    def __init__(self, max_memory_size: int = 1000, default_ttl: int = 1800):
        """Initialize the advanced cache manager."""
        self.redis_client = None
        self.memory_cache = OrderedDict()  # LRU cache
        self.cache_dependencies = {}  # Track cache dependencies
        self.cache_stats = {
            "l1_hits": 0,
            "l2_hits": 0,
            "l3_hits": 0,
            "misses": 0,
            "evictions": 0,
            "invalidations": 0,
            "warming_operations": 0
        }
        self.max_memory_cache_size = max_memory_size
        self.default_ttl = default_ttl
        self.cache_lock = threading.RLock()

        # Cache warming configuration
        self.warm_cache_enabled = True
        self.warm_cache_patterns = [
            "dashboard:*",
            "widget:*",
            "user_data:*"
        ]

        # Initialize Redis connection
        self._init_redis()

        # Start background tasks
        if self.redis_client:
            try:
                # Only create task if there's a running event loop
                loop = asyncio.get_running_loop()
                asyncio.create_task(self._start_cache_warming())
            except RuntimeError:
                # No event loop running, skip cache warming for now
                # This can happen during module imports
                logger.debug("No event loop running, skipping cache warming initialization")

    def _init_redis(self):
        """Initialize Redis connection with optimized settings."""
        try:
            self.redis_client = redis.from_url(
                REDIS_URL,
                decode_responses=False,  # Handle binary data
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            self.redis_client.ping()
            logger.info("Advanced Redis cache initialized successfully")

            # Configure Redis for optimal performance
            try:
                self.redis_client.config_set('maxmemory-policy', 'allkeys-lru')
                self.redis_client.config_set('timeout', '300')
            except Exception as e:
                logger.warning(f"Could not configure Redis settings: {e}")

        except Exception as e:
            logger.warning(f"Redis not available, using memory cache only: {e}")
            self.redis_client = None

    async def get(self, key: str, default: Any = None, track_dependencies: bool = True) -> Any:
        """
        Get value from multi-level cache with intelligent lookup.

        Args:
            key: Cache key
            default: Default value if not found
            track_dependencies: Whether to track cache dependencies

        Returns:
            Cached value or default
        """
        start_time = time.time()

        # Try L1 memory cache first (fastest)
        with self.cache_lock:
            if key in self.memory_cache:
                entry = self.memory_cache[key]
                if entry['expires'] > time.time():
                    # Update access statistics for LRU
                    entry['access_count'] = entry.get('access_count', 0) + 1
                    entry['last_access'] = time.time()
                    self.memory_cache.move_to_end(key)  # LRU update

                    self.cache_stats["l1_hits"] += 1
                    if hasattr(metrics, 'update_cache_performance'):
                        metrics.update_cache_performance("l1_hit", time.time() - start_time)
                    return entry['value']
                else:
                    # Expired, remove from memory cache
                    del self.memory_cache[key]

        # Try L2 Redis cache
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(f"datagenius:{key}")
                if cached_data:
                    value = pickle.loads(cached_data)
                    # Promote to L1 cache
                    self._set_memory_cache(key, value, self.default_ttl)

                    self.cache_stats["l2_hits"] += 1
                    if hasattr(metrics, 'update_cache_performance'):
                        metrics.update_cache_performance("l2_hit", time.time() - start_time)
                    return value
            except Exception as e:
                logger.warning(f"Redis cache error for key {key}: {e}")

        self.cache_stats["misses"] += 1
        if hasattr(metrics, 'update_cache_performance'):
            metrics.update_cache_performance("miss", time.time() - start_time)
        return default

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache with multi-layer storage.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful
        """
        ttl = ttl or self.default_ttl
        
        # Store in memory cache
        self._set_memory_cache(key, value, ttl)
        
        # Store in Redis cache
        if self.redis_client:
            try:
                serialized_value = pickle.dumps(value).decode('latin1')
                self.redis_client.setex(f"datagenius:{key}", ttl, serialized_value)
                return True
            except Exception as e:
                logger.warning(f"Redis cache set error: {e}")
        
        return True  # Memory cache succeeded

    def _set_memory_cache(self, key: str, value: Any, ttl: int):
        """Set value in L1 memory cache with LRU eviction."""
        with self.cache_lock:
            current_time = time.time()

            # Remove if already exists (for LRU ordering)
            if key in self.memory_cache:
                del self.memory_cache[key]

            # Clean up expired entries first
            expired_keys = [
                k for k, v in self.memory_cache.items()
                if v['expires'] <= current_time
            ]
            for expired_key in expired_keys:
                del self.memory_cache[expired_key]
                self.cache_stats["evictions"] += 1

            # Evict oldest entries if cache is full (LRU)
            while len(self.memory_cache) >= self.max_memory_cache_size:
                oldest_key = next(iter(self.memory_cache))
                del self.memory_cache[oldest_key]
                self.cache_stats["evictions"] += 1

            # Add new entry
            self.memory_cache[key] = {
                'value': value,
                'expires': current_time + ttl,
                'created': current_time,
                'access_count': 1,
                'last_access': current_time
            }

            # Move to end (most recently used)
            self.memory_cache.move_to_end(key)

    async def delete(self, key: str) -> bool:
        """Delete key from all cache layers and handle dependencies."""
        # Remove from memory cache
        with self.cache_lock:
            if key in self.memory_cache:
                del self.memory_cache[key]

        # Remove from Redis cache
        if self.redis_client:
            try:
                self.redis_client.delete(f"datagenius:{key}")
            except Exception as e:
                logger.warning(f"Redis cache delete error: {e}")

        # Handle dependent cache invalidation
        await self._invalidate_dependencies(key)

        return True

    async def _invalidate_dependencies(self, key: str):
        """Invalidate cache entries that depend on the given key."""
        if key in self.cache_dependencies:
            dependent_keys = self.cache_dependencies[key]
            for dependent_key in dependent_keys:
                await self.delete(dependent_key)
                self.cache_stats["invalidations"] += 1
            del self.cache_dependencies[key]

    def add_dependency(self, key: str, depends_on: str):
        """Add a cache dependency relationship."""
        if depends_on not in self.cache_dependencies:
            self.cache_dependencies[depends_on] = set()
        self.cache_dependencies[depends_on].add(key)

    async def _start_cache_warming(self):
        """Start background cache warming process."""
        if not self.warm_cache_enabled:
            return

        while True:
            try:
                await asyncio.sleep(300)  # Warm cache every 5 minutes
                await self._warm_frequently_accessed_data()
                self.cache_stats["warming_operations"] += 1
            except Exception as e:
                logger.error(f"Cache warming error: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry

    async def _warm_frequently_accessed_data(self):
        """Warm cache with frequently accessed data."""
        if not self.redis_client:
            return

        try:
            # Get frequently accessed keys from Redis
            for pattern in self.warm_cache_patterns:
                keys = self.redis_client.keys(f"datagenius:{pattern}")
                for redis_key in keys[:10]:  # Limit to top 10 per pattern
                    key = redis_key.replace("datagenius:", "")
                    if key not in self.memory_cache:
                        # Load into memory cache
                        await self.get(key)
        except Exception as e:
            logger.warning(f"Cache warming failed: {e}")

    async def clear_pattern(self, pattern: str):
        """Clear all keys matching a pattern."""
        # Clear from memory cache
        keys_to_delete = [k for k in self.memory_cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self.memory_cache[key]
        
        # Clear from Redis cache
        if self.redis_client:
            try:
                keys = self.redis_client.keys(f"datagenius:*{pattern}*")
                if keys:
                    self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis pattern clear error: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        total_hits = self.cache_stats.get("l1_hits", 0) + self.cache_stats.get("l2_hits", 0) + self.cache_stats.get("l3_hits", 0)
        # Fallback to old stats format if new format not available
        if total_hits == 0:
            total_hits = self.cache_stats.get("hits", 0)

        total_requests = total_hits + self.cache_stats.get("misses", 0)
        hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0

        l1_hit_rate = (self.cache_stats.get("l1_hits", 0) / total_requests * 100) if total_requests > 0 else 0
        l2_hit_rate = (self.cache_stats.get("l2_hits", 0) / total_requests * 100) if total_requests > 0 else 0

        return {
            "memory_cache_size": len(self.memory_cache),
            "memory_cache_max_size": self.max_memory_cache_size,
            "hit_rate_percent": round(hit_rate, 2),
            "l1_hit_rate_percent": round(l1_hit_rate, 2),
            "l2_hit_rate_percent": round(l2_hit_rate, 2),
            "total_hits": total_hits,
            "l1_hits": self.cache_stats.get("l1_hits", 0),
            "l2_hits": self.cache_stats.get("l2_hits", 0),
            "l3_hits": self.cache_stats.get("l3_hits", 0),
            "total_misses": self.cache_stats.get("misses", 0),
            "total_evictions": self.cache_stats.get("evictions", 0),
            "total_invalidations": self.cache_stats.get("invalidations", 0),
            "warming_operations": self.cache_stats.get("warming_operations", 0),
            "cache_dependencies_count": len(self.cache_dependencies),
            "redis_available": self.redis_client is not None,
            "total_requests": total_requests
        }

    async def get_memory_usage(self) -> Dict[str, Any]:
        """Get detailed memory usage statistics."""
        import sys

        memory_usage = {
            "memory_cache_entries": len(self.memory_cache),
            "cache_dependencies": len(self.cache_dependencies),
            "estimated_memory_bytes": 0
        }

        # Estimate memory usage of cache entries
        try:
            with self.cache_lock:
                for key, value in self.memory_cache.items():
                    memory_usage["estimated_memory_bytes"] += sys.getsizeof(key)
                    memory_usage["estimated_memory_bytes"] += sys.getsizeof(value)
        except Exception as e:
            logger.warning(f"Memory usage calculation error: {e}")

        return memory_usage


# Global cache manager instance
cache_manager = AdvancedCacheManager()


def cached(ttl: int = 3600, key_prefix: str = ""):
    """
    Decorator for caching function results.
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache key
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For synchronous functions, use asyncio to handle cache operations
            cache_key = f"{key_prefix}{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache (simplified for sync)
            if cache_key in cache_manager.memory_cache:
                entry = cache_manager.memory_cache[cache_key]
                if entry['expires'] > time.time():
                    return entry['value']
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager._set_memory_cache(cache_key, result, ttl)
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


class EnhancedConnectionPoolManager:
    """
    Enhanced database connection pool manager with Phase 1 optimizations.

    Features:
    - Optimized connection pooling
    - Query performance monitoring
    - Automatic query optimization
    - Connection health monitoring
    - Prepared statement caching
    """

    def __init__(self):
        """Initialize the enhanced connection pool manager."""
        self.pool_stats = {
            "active_connections": 0,
            "total_connections": 0,
            "connection_errors": 0,
            "query_count": 0,
            "avg_query_time": 0.0,
            "slow_queries": 0,
            "prepared_statements": 0,
            "connection_reuses": 0
        }
        self.query_times = []
        self.slow_query_threshold = 1.0  # 1 second
        self.max_query_history = 1000
        self.prepared_statements_cache = {}
        self.connection_health_checks = {}

        # Query optimization settings
        self.query_optimizations_enabled = True
        self.auto_explain_slow_queries = True

    def record_query_time(self, query_time: float, query_type: str = "unknown"):
        """Record query execution time with enhanced monitoring."""
        self.query_times.append(query_time)
        self.pool_stats["query_count"] += 1

        # Track slow queries
        if query_time > self.slow_query_threshold:
            self.pool_stats["slow_queries"] += 1
            logger.warning(f"Slow query detected: {query_time:.2f}s for {query_type}")

        # Keep only recent query times
        if len(self.query_times) > self.max_query_history:
            self.query_times = self.query_times[-self.max_query_history:]

        # Update average
        self.pool_stats["avg_query_time"] = sum(self.query_times) / len(self.query_times)

        # Update metrics
        if hasattr(metrics, 'update_database_query_time'):
            metrics.update_database_query_time(query_type, query_time)

    @asynccontextmanager
    async def get_connection(self):
        """Get optimized database connection with health monitoring."""
        Session = sessionmaker(bind=engine)
        session = Session()
        connection_id = id(session)

        try:
            self.pool_stats["active_connections"] += 1
            self.pool_stats["total_connections"] += 1

            # Check if this is a reused connection
            if connection_id in self.connection_health_checks:
                self.pool_stats["connection_reuses"] += 1

            # Apply query optimizations
            if self.query_optimizations_enabled:
                await self._apply_connection_optimizations(session)

            # Update metrics
            if hasattr(metrics, 'update_database_connections'):
                metrics.update_database_connections(self.pool_stats["active_connections"])

            # Track connection health
            self.connection_health_checks[connection_id] = time.time()

            yield session

        except Exception as e:
            self.pool_stats["connection_errors"] += 1
            logger.error(f"Database connection error: {e}")
            session.rollback()
            raise
        finally:
            session.close()
            self.pool_stats["active_connections"] -= 1
            if hasattr(metrics, 'update_database_connections'):
                metrics.update_database_connections(self.pool_stats["active_connections"])

            # Clean up connection health tracking
            if connection_id in self.connection_health_checks:
                del self.connection_health_checks[connection_id]

    async def _apply_connection_optimizations(self, session):
        """Apply connection-level optimizations using secure SQL execution."""
        try:
            # Enable query plan caching
            await execute_secure_sql(session, "SET plan_cache_mode = force_generic_plan")

            # Set optimal work memory for dashboard queries
            await execute_secure_sql(session, "SET work_mem = '256MB'")

            # Enable parallel query execution
            await execute_secure_sql(session, "SET max_parallel_workers_per_gather = 4")

            # Optimize for dashboard-specific queries
            await execute_secure_sql(session, "SET random_page_cost = 1.1")  # SSD optimization
            await execute_secure_sql(session, "SET effective_cache_size = '4GB'")

        except Exception as e:
            logger.warning(f"Could not apply connection optimizations: {e}")

    async def optimize_queries(self, session):
        """Apply query optimizations (legacy method)."""
        await self._apply_connection_optimizations(session)

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive connection pool statistics."""
        stats = self.pool_stats.copy()

        # Add calculated metrics
        if self.pool_stats["query_count"] > 0:
            stats["slow_query_percentage"] = (
                self.pool_stats["slow_queries"] / self.pool_stats["query_count"] * 100
            )
        else:
            stats["slow_query_percentage"] = 0.0

        stats["connection_health_checks"] = len(self.connection_health_checks)
        stats["prepared_statements_cached"] = len(self.prepared_statements_cache)

        return stats

    async def cleanup_stale_connections(self):
        """Clean up stale connection health checks."""
        current_time = time.time()
        stale_threshold = 3600  # 1 hour

        stale_connections = [
            conn_id for conn_id, last_seen in self.connection_health_checks.items()
            if current_time - last_seen > stale_threshold
        ]

        for conn_id in stale_connections:
            del self.connection_health_checks[conn_id]


# Global connection pool manager
pool_manager = EnhancedConnectionPoolManager()


class MemoryManager:
    """
    Memory usage optimization and garbage collection management.
    """

    def __init__(self):
        """Initialize memory manager."""
        self.object_registry = weakref.WeakSet()
        self.memory_stats = {
            "gc_collections": 0,
            "objects_tracked": 0,
            "memory_freed": 0
        }

    def register_object(self, obj: Any):
        """Register object for memory tracking."""
        self.object_registry.add(obj)
        self.memory_stats["objects_tracked"] += 1

    async def cleanup_memory(self):
        """Perform memory cleanup operations."""
        import gc
        
        # Force garbage collection
        collected = gc.collect()
        self.memory_stats["gc_collections"] += 1
        self.memory_stats["memory_freed"] += collected
        
        # Clear expired cache entries
        await self._cleanup_expired_cache()
        
        logger.info(f"Memory cleanup completed, freed {collected} objects")

    async def _cleanup_expired_cache(self):
        """Clean up expired cache entries."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in cache_manager.memory_cache.items()
            if entry['expires'] <= current_time
        ]
        
        for key in expired_keys:
            del cache_manager.memory_cache[key]
            cache_manager.cache_stats["evictions"] += 1

    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
            "percent": round(process.memory_percent(), 2),
            "objects_tracked": len(self.object_registry),
            "gc_stats": self.memory_stats
        }


# Global memory manager
memory_manager = MemoryManager()


class ResponseTimeOptimizer:
    """
    Response time optimization through various techniques.
    """

    def __init__(self):
        """Initialize response time optimizer."""
        self.response_times = []
        self.max_history = 1000

    def record_response_time(self, endpoint: str, duration: float):
        """Record response time for analysis."""
        self.response_times.append({
            "endpoint": endpoint,
            "duration": duration,
            "timestamp": time.time()
        })
        
        # Keep only recent history
        if len(self.response_times) > self.max_history:
            self.response_times = self.response_times[-self.max_history:]

    def get_performance_insights(self) -> Dict[str, Any]:
        """Get performance insights and recommendations."""
        if not self.response_times:
            return {"message": "No performance data available"}
        
        # Calculate statistics
        durations = [rt["duration"] for rt in self.response_times]
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)
        
        # Identify slow endpoints
        endpoint_stats = {}
        for rt in self.response_times:
            endpoint = rt["endpoint"]
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = []
            endpoint_stats[endpoint].append(rt["duration"])
        
        slow_endpoints = []
        for endpoint, times in endpoint_stats.items():
            avg_time = sum(times) / len(times)
            if avg_time > avg_duration * 1.5:  # 50% slower than average
                slow_endpoints.append({
                    "endpoint": endpoint,
                    "avg_duration": round(avg_time, 3),
                    "requests": len(times)
                })
        
        return {
            "avg_response_time": round(avg_duration, 3),
            "max_response_time": round(max_duration, 3),
            "min_response_time": round(min_duration, 3),
            "total_requests": len(self.response_times),
            "slow_endpoints": sorted(slow_endpoints, key=lambda x: x["avg_duration"], reverse=True)[:5]
        }


# Global response time optimizer
response_optimizer = ResponseTimeOptimizer()


async def optimize_performance():
    """
    Run comprehensive performance optimization.
    """
    logger.info("Starting performance optimization")
    
    # Memory cleanup
    await memory_manager.cleanup_memory()
    
    # Cache optimization
    cache_stats = cache_manager.get_stats()
    if cache_stats["hit_rate"] < 70:  # Less than 70% hit rate
        logger.warning(f"Low cache hit rate: {cache_stats['hit_rate']}%")
    
    # Performance insights
    insights = response_optimizer.get_performance_insights()
    if insights.get("slow_endpoints"):
        logger.warning(f"Slow endpoints detected: {len(insights['slow_endpoints'])}")
    
    logger.info("Performance optimization completed")


def performance_monitor(func):
    """Decorator to monitor function performance."""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            endpoint = getattr(func, '__name__', 'unknown')
            response_optimizer.record_response_time(endpoint, duration)
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            endpoint = getattr(func, '__name__', 'unknown')
            response_optimizer.record_response_time(endpoint, duration)
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


# Periodic optimization task
async def start_performance_monitoring():
    """Start background performance monitoring."""
    while True:
        try:
            await optimize_performance()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Performance monitoring error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute on error


# Initialize performance monitoring
def initialize_performance_optimization():
    """Initialize performance optimization system."""
    asyncio.create_task(start_performance_monitoring())
    logger.info("Performance optimization system initialized")
