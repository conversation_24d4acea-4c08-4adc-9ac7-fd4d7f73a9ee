"""
Environment-specific security policies.

This module provides security policies that are automatically applied
based on the current environment (development, staging, production).
"""

import os
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class Environment(Enum):
    """Application environments."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    enforce_https: bool
    rate_limiting_enabled: bool
    rate_limit_requests_per_minute: int
    rate_limit_burst_size: int
    enable_audit_logging: bool
    enable_input_validation: bool
    enable_output_sanitization: bool
    enable_sql_injection_protection: bool
    enable_xss_protection: bool
    enable_csrf_protection: bool
    max_upload_size_mb: int
    session_timeout_minutes: int
    max_concurrent_sessions: int
    require_strong_passwords: bool
    enable_2fa: bool
    log_level: str
    debug_mode: bool
    cors_strict_mode: bool
    allowed_cors_origins: List[str]


class SecurityPolicyManager:
    """Manages environment-specific security policies."""
    
    def __init__(self):
        self.current_environment = self._detect_environment()
        self.policies = self._load_policies()
    
    def _detect_environment(self) -> Environment:
        """Detect current environment from environment variables."""
        env_name = os.getenv("ENVIRONMENT", "development").lower()
        
        try:
            return Environment(env_name)
        except ValueError:
            logger.warning(f"Unknown environment '{env_name}', defaulting to development")
            return Environment.DEVELOPMENT
    
    def _load_policies(self) -> Dict[Environment, SecurityPolicy]:
        """Load security policies for all environments."""
        return {
            Environment.DEVELOPMENT: SecurityPolicy(
                enforce_https=False,
                rate_limiting_enabled=True,
                rate_limit_requests_per_minute=1000,
                rate_limit_burst_size=100,
                enable_audit_logging=True,
                enable_input_validation=True,
                enable_output_sanitization=True,
                enable_sql_injection_protection=True,
                enable_xss_protection=True,
                enable_csrf_protection=True,
                max_upload_size_mb=10,
                session_timeout_minutes=480,  # 8 hours for development
                max_concurrent_sessions=10,
                require_strong_passwords=True,
                enable_2fa=False,
                log_level="DEBUG",
                debug_mode=True,
                cors_strict_mode=False,
                allowed_cors_origins=["http://localhost:3000", "http://localhost:5173"]
            ),
            
            Environment.STAGING: SecurityPolicy(
                enforce_https=True,
                rate_limiting_enabled=True,
                rate_limit_requests_per_minute=500,
                rate_limit_burst_size=50,
                enable_audit_logging=True,
                enable_input_validation=True,
                enable_output_sanitization=True,
                enable_sql_injection_protection=True,
                enable_xss_protection=True,
                enable_csrf_protection=True,
                max_upload_size_mb=10,
                session_timeout_minutes=240,  # 4 hours for staging
                max_concurrent_sessions=5,
                require_strong_passwords=True,
                enable_2fa=True,
                log_level="INFO",
                debug_mode=False,
                cors_strict_mode=True,
                allowed_cors_origins=["https://staging.datagenius.com"]
            ),
            
            Environment.PRODUCTION: SecurityPolicy(
                enforce_https=True,
                rate_limiting_enabled=True,
                rate_limit_requests_per_minute=100,
                rate_limit_burst_size=20,
                enable_audit_logging=True,
                enable_input_validation=True,
                enable_output_sanitization=True,
                enable_sql_injection_protection=True,
                enable_xss_protection=True,
                enable_csrf_protection=True,
                max_upload_size_mb=5,
                session_timeout_minutes=120,  # 2 hours for production
                max_concurrent_sessions=3,
                require_strong_passwords=True,
                enable_2fa=True,
                log_level="WARNING",
                debug_mode=False,
                cors_strict_mode=True,
                allowed_cors_origins=["https://datagenius.com"]
            ),
            
            Environment.TESTING: SecurityPolicy(
                enforce_https=False,
                rate_limiting_enabled=False,  # Disabled for testing
                rate_limit_requests_per_minute=10000,
                rate_limit_burst_size=1000,
                enable_audit_logging=False,  # Disabled for testing
                enable_input_validation=True,
                enable_output_sanitization=True,
                enable_sql_injection_protection=True,
                enable_xss_protection=True,
                enable_csrf_protection=False,  # Disabled for testing
                max_upload_size_mb=1,
                session_timeout_minutes=60,
                max_concurrent_sessions=1,
                require_strong_passwords=False,  # Simplified for testing
                enable_2fa=False,
                log_level="ERROR",
                debug_mode=False,
                cors_strict_mode=False,
                allowed_cors_origins=["*"]
            )
        }
    
    def get_current_policy(self) -> SecurityPolicy:
        """Get security policy for current environment."""
        return self.policies[self.current_environment]
    
    def get_policy(self, environment: Environment) -> SecurityPolicy:
        """Get security policy for specific environment."""
        return self.policies[environment]
    
    def apply_policy(self) -> Dict[str, Any]:
        """Apply current security policy and return configuration."""
        policy = self.get_current_policy()
        
        logger.info(f"Applying security policy for environment: {self.current_environment.value}")
        
        # Set environment variables based on policy
        env_config = {
            "ENFORCE_HTTPS": str(policy.enforce_https).lower(),
            "RATE_LIMITING_ENABLED": str(policy.rate_limiting_enabled).lower(),
            "RATE_LIMIT_REQUESTS": str(policy.rate_limit_requests_per_minute),
            "RATE_LIMIT_BURST": str(policy.rate_limit_burst_size),
            "ENABLE_AUDIT_LOGGING": str(policy.enable_audit_logging).lower(),
            "ENABLE_INPUT_VALIDATION": str(policy.enable_input_validation).lower(),
            "ENABLE_OUTPUT_SANITIZATION": str(policy.enable_output_sanitization).lower(),
            "MAX_UPLOAD_SIZE_MB": str(policy.max_upload_size_mb),
            "SESSION_TIMEOUT_MINUTES": str(policy.session_timeout_minutes),
            "MAX_CONCURRENT_SESSIONS": str(policy.max_concurrent_sessions),
            "REQUIRE_STRONG_PASSWORDS": str(policy.require_strong_passwords).lower(),
            "ENABLE_2FA": str(policy.enable_2fa).lower(),
            "LOG_LEVEL": policy.log_level,
            "DEBUG": str(policy.debug_mode).lower(),
            "CORS_ORIGINS": ",".join(policy.allowed_cors_origins)
        }
        
        # Apply environment variables
        for key, value in env_config.items():
            if key not in os.environ:  # Don't override existing env vars
                os.environ[key] = value
        
        # Log security policy application
        logger.info(f"Security policy applied: {len(env_config)} settings configured")
        
        if policy.debug_mode and self.current_environment == Environment.PRODUCTION:
            logger.critical("DEBUG MODE IS ENABLED IN PRODUCTION - SECURITY RISK!")
        
        return env_config
    
    def validate_policy_compliance(self) -> List[str]:
        """Validate that current configuration complies with security policy."""
        policy = self.get_current_policy()
        violations = []
        
        # Check HTTPS enforcement
        if policy.enforce_https and os.getenv("ENFORCE_HTTPS", "false").lower() != "true":
            violations.append("HTTPS enforcement required but not enabled")
        
        # Check rate limiting
        if policy.rate_limiting_enabled and os.getenv("RATE_LIMITING_ENABLED", "false").lower() != "true":
            violations.append("Rate limiting required but not enabled")
        
        # Check debug mode in production
        if (self.current_environment == Environment.PRODUCTION and 
            os.getenv("DEBUG", "false").lower() == "true"):
            violations.append("Debug mode must be disabled in production")
        
        # Check 2FA requirement
        if policy.enable_2fa and os.getenv("ENABLE_2FA", "false").lower() != "true":
            violations.append("Two-factor authentication required but not enabled")
        
        # Check audit logging
        if policy.enable_audit_logging and os.getenv("ENABLE_AUDIT_LOGGING", "false").lower() != "true":
            violations.append("Audit logging required but not enabled")
        
        return violations
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security configuration summary."""
        policy = self.get_current_policy()
        violations = self.validate_policy_compliance()
        
        return {
            "environment": self.current_environment.value,
            "policy_compliant": len(violations) == 0,
            "violations": violations,
            "security_features": {
                "https_enforced": policy.enforce_https,
                "rate_limiting": policy.rate_limiting_enabled,
                "input_validation": policy.enable_input_validation,
                "output_sanitization": policy.enable_output_sanitization,
                "sql_injection_protection": policy.enable_sql_injection_protection,
                "xss_protection": policy.enable_xss_protection,
                "csrf_protection": policy.enable_csrf_protection,
                "audit_logging": policy.enable_audit_logging,
                "two_factor_auth": policy.enable_2fa,
                "strong_passwords": policy.require_strong_passwords
            },
            "limits": {
                "max_upload_size_mb": policy.max_upload_size_mb,
                "session_timeout_minutes": policy.session_timeout_minutes,
                "max_concurrent_sessions": policy.max_concurrent_sessions,
                "rate_limit_per_minute": policy.rate_limit_requests_per_minute,
                "rate_limit_burst": policy.rate_limit_burst_size
            }
        }


# Global policy manager instance
security_policy_manager = SecurityPolicyManager()


def apply_environment_security_policy() -> Dict[str, Any]:
    """Apply security policy for current environment."""
    return security_policy_manager.apply_policy()


def get_current_security_policy() -> SecurityPolicy:
    """Get current security policy."""
    return security_policy_manager.get_current_policy()


def validate_security_compliance() -> List[str]:
    """Validate security policy compliance."""
    return security_policy_manager.validate_policy_compliance()


def get_security_summary() -> Dict[str, Any]:
    """Get security configuration summary."""
    return security_policy_manager.get_security_summary()
