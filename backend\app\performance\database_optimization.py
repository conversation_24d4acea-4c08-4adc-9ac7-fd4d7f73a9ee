"""
Database optimization module for Phase 3 performance improvements.

This module implements comprehensive database-level optimizations including:
- Index creation and optimization
- Query performance monitoring
- Database statistics collection
- Connection pool optimization
- Batch operations for CRUD functions
- Query caching mechanisms
- Performance profiling and metrics
"""

import logging
from typing import Dict, Any, List, Optional, TypeVar
from datetime import datetime, timedelta
from sqlalchemy import text, Index, event, func, select, update, delete, insert
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session
from contextlib import contextmanager
import time
import json
from collections import defaultdict

from ..database import engine, get_db, Base
from ..models.dashboard_customization import Dashboard, DashboardSection, DashboardWidget
from ..config import DATABASE_URL
from ..redis_client import redis_client

logger = logging.getLogger(__name__)

# Type variables for generic batch operations
T = TypeVar('T')


class DatabaseOptimizer:
    """
    Database optimization manager for Phase 1 performance improvements.
    
    Target improvements:
    - Reduce dashboard load time from ~2.5s to < 1.5s
    - Optimize database queries per load from ~15 to < 8
    - Improve query response times
    """

    def __init__(self):
        """Initialize the database optimizer."""
        self.optimization_stats = {
            "indexes_created": 0,
            "queries_optimized": 0,
            "performance_improvements": []
        }
        self.is_postgresql = "postgresql" in DATABASE_URL.lower()

    async def apply_dashboard_optimizations(self) -> Dict[str, Any]:
        """Apply all dashboard-specific database optimizations."""
        logger.info("Starting Phase 1 database optimizations...")
        
        results = {
            "indexes_created": 0,
            "optimizations_applied": [],
            "errors": []
        }
        
        try:
            # Create performance indexes
            indexes_created = await self._create_performance_indexes()
            results["indexes_created"] = indexes_created
            
            # Apply PostgreSQL-specific optimizations
            if self.is_postgresql:
                pg_optimizations = await self._apply_postgresql_optimizations()
                results["optimizations_applied"].extend(pg_optimizations)
            
            # Update database statistics
            await self._update_database_statistics()
            results["optimizations_applied"].append("database_statistics_updated")
            
            logger.info(f"Database optimizations completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            results["errors"].append(str(e))
            return results

    async def _create_performance_indexes(self) -> int:
        """Create performance indexes for dashboard queries."""
        indexes_to_create = [
            # Dashboard table indexes
            {
                "table": "dashboards",
                "name": "idx_dashboards_user_default",
                "columns": ["user_id", "is_default"],
                "condition": "WHERE is_default = true"
            },
            {
                "table": "dashboards",
                "name": "idx_dashboards_user_updated",
                "columns": ["user_id", "updated_at"],
                "condition": None
            },
            
            # Dashboard sections indexes
            {
                "table": "dashboard_sections",
                "name": "idx_dashboard_sections_dashboard_position",
                "columns": ["dashboard_id", "position"],
                "condition": "WHERE is_active = true"
            },
            {
                "table": "dashboard_sections",
                "name": "idx_dashboard_sections_user_active",
                "columns": ["user_id", "is_active"],
                "condition": "WHERE is_active = true"
            },
            
            # Dashboard widgets indexes
            {
                "table": "dashboard_widgets",
                "name": "idx_dashboard_widgets_section_active",
                "columns": ["section_id", "is_active"],
                "condition": "WHERE is_active = true"
            },
            {
                "table": "dashboard_widgets",
                "name": "idx_dashboard_widgets_user_type",
                "columns": ["user_id", "widget_type"],
                "condition": None
            },
            
            # GIN indexes for JSON columns (PostgreSQL only)
            {
                "table": "dashboard_widgets",
                "name": "idx_dashboard_widgets_data_config_gin",
                "columns": ["data_config"],
                "type": "GIN",
                "operator_class": "jsonb_ops",
                "condition": "WHERE data_config IS NOT NULL"
            },
            {
                "table": "dashboard_sections",
                "name": "idx_dashboard_sections_layout_config_gin",
                "columns": ["layout_config"],
                "type": "GIN",
                "operator_class": "jsonb_ops",
                "condition": "WHERE layout_config IS NOT NULL"
            }
        ]
        
        created_count = 0
        db = next(get_db())
        
        try:
            for index_def in indexes_to_create:
                try:
                    # Skip GIN indexes for non-PostgreSQL databases
                    if index_def.get("type") == "GIN" and not self.is_postgresql:
                        continue
                    
                    # Check if index already exists
                    if await self._index_exists(db, index_def["name"]):
                        logger.info(f"Index {index_def['name']} already exists, skipping")
                        continue
                    
                    # Create the index
                    await self._create_index(db, index_def)
                    created_count += 1
                    logger.info(f"Created index: {index_def['name']}")
                    
                except Exception as e:
                    logger.warning(f"Failed to create index {index_def['name']}: {e}")
                    
        finally:
            db.close()
        
        return created_count

    async def _create_index(self, db: Session, index_def: Dict[str, Any]):
        """Create a single database index."""
        table_name = index_def["table"]
        index_name = index_def["name"]
        columns = index_def["columns"]
        index_type = index_def.get("type", "BTREE")
        operator_class = index_def.get("operator_class", "")
        condition = index_def.get("condition", "")

        if self.is_postgresql:
            # PostgreSQL syntax - use autocommit for CONCURRENTLY indexes
            if index_type == "GIN":
                # For GIN indexes on JSON columns, we need to specify the operator class
                if operator_class:
                    columns_str = ", ".join([f"{col} {operator_class}" for col in columns])
                else:
                    columns_str = ", ".join(columns)
                sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} USING GIN ({columns_str})"
            else:
                columns_str = ", ".join(columns)
                sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"

            if condition:
                sql += f" {condition}"

            # For PostgreSQL, we need to use autocommit mode for CONCURRENTLY
            # But since CONCURRENTLY can cause issues, we'll use regular CREATE INDEX
            connection = db.get_bind().connect()
            try:
                # Use autocommit mode
                connection = connection.execution_options(autocommit=True)
                connection.execute(text(sql))
            finally:
                connection.close()
        else:
            # Generic SQL syntax
            columns_str = ", ".join(columns)
            sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"
            db.execute(text(sql))
            db.commit()

    async def _index_exists(self, db: Session, index_name: str) -> bool:
        """Check if an index already exists."""
        try:
            if self.is_postgresql:
                result = db.execute(text("""
                    SELECT 1 FROM pg_indexes
                    WHERE indexname = :index_name
                """), {"index_name": index_name})
            else:
                # SQLite
                result = db.execute(text("""
                    SELECT 1 FROM sqlite_master
                    WHERE type = 'index' AND name = :index_name
                """), {"index_name": index_name})

            return result.fetchone() is not None
        except Exception as e:
            logger.warning(f"Error checking if index {index_name} exists: {e}")
            # If we can't check, assume it doesn't exist and try to create it
            return False

    async def _apply_postgresql_optimizations(self) -> List[str]:
        """Apply PostgreSQL-specific optimizations."""
        optimizations = []

        try:
            # Use engine directly for ALTER SYSTEM commands (cannot run in transaction)
            with engine.connect() as conn:
                # Set autocommit mode for ALTER SYSTEM commands
                conn.execute(text("COMMIT"))  # End any existing transaction

                try:
                    # Enable auto-vacuum for better performance
                    conn.execute(text("ALTER SYSTEM SET autovacuum = on"))
                    optimizations.append("autovacuum_enabled")
                except Exception as e:
                    logger.warning(f"Failed to set autovacuum: {e}")

                try:
                    # Optimize shared buffers for dashboard workload
                    conn.execute(text("ALTER SYSTEM SET shared_buffers = '256MB'"))
                    optimizations.append("shared_buffers_optimized")
                except Exception as e:
                    logger.warning(f"Failed to set shared_buffers: {e}")

                try:
                    # Optimize work memory for complex queries
                    conn.execute(text("ALTER SYSTEM SET work_mem = '64MB'"))
                    optimizations.append("work_mem_optimized")
                except Exception as e:
                    logger.warning(f"Failed to set work_mem: {e}")

                try:
                    # Reload configuration
                    conn.execute(text("SELECT pg_reload_conf()"))
                    optimizations.append("configuration_reloaded")
                except Exception as e:
                    logger.warning(f"Failed to reload configuration: {e}")

            logger.info(f"Applied PostgreSQL optimizations: {optimizations}")

        except Exception as e:
            logger.warning(f"PostgreSQL optimization failed: {e}")

        return optimizations

    async def _update_database_statistics(self):
        """Update database statistics for better query planning."""
        db = next(get_db())
        
        try:
            if self.is_postgresql:
                # Update statistics for dashboard tables
                tables = ["dashboards", "dashboard_sections", "dashboard_widgets"]
                for table in tables:
                    db.execute(text(f"ANALYZE {table}"))
                logger.info("PostgreSQL statistics updated")
            else:
                # SQLite
                db.execute(text("ANALYZE"))
                logger.info("SQLite statistics updated")
                
        except Exception as e:
            logger.warning(f"Statistics update failed: {e}")
        finally:
            db.close()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        db = next(get_db())
        metrics = {}

        # Check if performance monitoring is disabled
        import os
        if os.getenv("DISABLE_DB_PERFORMANCE_MONITORING", "false").lower() == "true":
            logger.info("Database performance monitoring is disabled")
            return {
                "table_stats": [],
                "index_stats": [],
                "dashboard_count": 0,
                "widget_count": 0,
                "monitoring_disabled": True
            }

        try:
            if self.is_postgresql:
                # Get PostgreSQL-specific metrics with version compatibility
                try:
                    # Try with relname first (more compatible) - remove await since db.execute is synchronous
                    result = db.execute(text("""
                        SELECT
                            schemaname,
                            relname as table_name,
                            n_tup_ins as inserts,
                            n_tup_upd as updates,
                            n_tup_del as deletes,
                            n_live_tup as live_tuples,
                            n_dead_tup as dead_tuples
                        FROM pg_stat_user_tables
                        WHERE relname IN ('dashboards', 'dashboard_sections', 'dashboard_widgets')
                    """))

                    # Convert rows to dictionaries safely
                    rows = result.fetchall()
                    metrics["table_stats"] = []
                    for row in rows:
                        try:
                            if hasattr(row, '_mapping'):
                                # SQLAlchemy Row object with _mapping attribute
                                metrics["table_stats"].append(dict(row._mapping))
                            elif hasattr(row, 'keys'):
                                # Row object with keys method
                                metrics["table_stats"].append({key: row[key] for key in row.keys()})
                            else:
                                # Fallback: assume it's a tuple and create dict with column names
                                columns = ['schemaname', 'table_name', 'inserts', 'updates', 'deletes', 'live_tuples', 'dead_tuples']
                                metrics["table_stats"].append(dict(zip(columns, row)))
                        except Exception as row_error:
                            logger.warning(f"Failed to convert row to dict: {row_error}")
                            continue

                except Exception as e:
                    logger.warning(f"Failed to get table stats with relname, trying alternative: {e}")
                    try:
                        # Fallback: try with basic query without specific table filtering
                        result = db.execute(text("""
                            SELECT
                                schemaname,
                                'unknown' as table_name,
                                COALESCE(SUM(n_tup_ins), 0) as inserts,
                                COALESCE(SUM(n_tup_upd), 0) as updates,
                                COALESCE(SUM(n_tup_del), 0) as deletes,
                                COALESCE(SUM(n_live_tup), 0) as live_tuples,
                                COALESCE(SUM(n_dead_tup), 0) as dead_tuples
                            FROM pg_stat_user_tables
                            WHERE schemaname = 'public'
                            GROUP BY schemaname
                        """))

                        # Convert rows to dictionaries safely
                        rows = result.fetchall()
                        metrics["table_stats"] = []
                        for row in rows:
                            try:
                                if hasattr(row, '_mapping'):
                                    metrics["table_stats"].append(dict(row._mapping))
                                elif hasattr(row, 'keys'):
                                    metrics["table_stats"].append({key: row[key] for key in row.keys()})
                                else:
                                    columns = ['schemaname', 'table_name', 'inserts', 'updates', 'deletes', 'live_tuples', 'dead_tuples']
                                    metrics["table_stats"].append(dict(zip(columns, row)))
                            except Exception as row_error:
                                logger.warning(f"Failed to convert row to dict: {row_error}")
                                continue

                    except Exception as e2:
                        logger.warning(f"Failed to get table stats with fallback query: {e2}")
                        metrics["table_stats"] = []

                # Get index usage statistics with error handling
                try:
                    result = db.execute(text("""
                        SELECT
                            indexrelname as index_name,
                            idx_tup_read,
                            idx_tup_fetch
                        FROM pg_stat_user_indexes
                        WHERE schemaname = 'public'
                        AND indexrelname LIKE 'idx_dashboard%'
                    """))

                    # Convert rows to dictionaries safely
                    rows = result.fetchall()
                    metrics["index_stats"] = []
                    for row in rows:
                        try:
                            if hasattr(row, '_mapping'):
                                metrics["index_stats"].append(dict(row._mapping))
                            elif hasattr(row, 'keys'):
                                metrics["index_stats"].append({key: row[key] for key in row.keys()})
                            else:
                                columns = ['index_name', 'idx_tup_read', 'idx_tup_fetch']
                                metrics["index_stats"].append(dict(zip(columns, row)))
                        except Exception as row_error:
                            logger.warning(f"Failed to convert index row to dict: {row_error}")
                            continue

                except Exception as e:
                    logger.warning(f"Failed to get index stats: {e}")
                    metrics["index_stats"] = []
            
            # Get general table sizes
            try:
                result = db.execute(text("""
                    SELECT
                        COUNT(*) as dashboard_count
                    FROM dashboards
                """))
                metrics["dashboard_count"] = result.fetchone()[0]

                result = db.execute(text("""
                    SELECT
                        COUNT(*) as widget_count
                    FROM dashboard_widgets
                """))
                metrics["widget_count"] = result.fetchone()[0]
            except Exception as e:
                logger.warning(f"Failed to get table counts: {e}")
                metrics["dashboard_count"] = 0
                metrics["widget_count"] = 0
            
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
        finally:
            db.close()
        
        return metrics


# Global database optimizer instance
db_optimizer = DatabaseOptimizer()


class BatchOperationManager:
    """
    Manager for batch database operations to optimize performance.

    Provides batch insert, update, and delete operations to reduce
    database round trips and improve performance.
    """

    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.performance_metrics = defaultdict(list)

    @contextmanager
    def batch_session(self):
        """Context manager for batch operations with automatic commit/rollback."""
        db = next(get_db())
        try:
            yield db
            db.commit()
        except Exception as e:
            db.rollback()
            logger.error(f"Batch operation failed: {e}")
            raise
        finally:
            db.close()

    def batch_insert(self, db: Session, model_class: T, data_list: List[Dict[str, Any]]) -> List[T]:
        """
        Perform batch insert operation.

        Args:
            db: Database session
            model_class: SQLAlchemy model class
            data_list: List of dictionaries containing data to insert

        Returns:
            List of created model instances
        """
        start_time = time.time()
        created_objects = []

        try:
            # Process in batches to avoid memory issues
            for i in range(0, len(data_list), self.batch_size):
                batch = data_list[i:i + self.batch_size]

                # Create model instances
                batch_objects = [model_class(**data) for data in batch]

                # Add all objects to session
                db.add_all(batch_objects)
                db.flush()  # Flush to get IDs without committing

                created_objects.extend(batch_objects)

            execution_time = time.time() - start_time
            self.performance_metrics['batch_insert'].append({
                'count': len(data_list),
                'execution_time': execution_time,
                'timestamp': datetime.now()
            })

            logger.info(f"Batch inserted {len(data_list)} {model_class.__name__} records in {execution_time:.2f}s")
            return created_objects

        except Exception as e:
            logger.error(f"Batch insert failed for {model_class.__name__}: {e}")
            raise

    def batch_update(self, db: Session, model_class: T, updates: List[Dict[str, Any]],
                    id_field: str = 'id') -> int:
        """
        Perform batch update operation.

        Args:
            db: Database session
            model_class: SQLAlchemy model class
            updates: List of dictionaries containing id and update data
            id_field: Name of the ID field

        Returns:
            Number of updated records
        """
        start_time = time.time()
        updated_count = 0

        try:
            # Process in batches
            for i in range(0, len(updates), self.batch_size):
                batch = updates[i:i + self.batch_size]

                # Perform bulk update
                for update_data in batch:
                    record_id = update_data.pop(id_field)
                    result = db.query(model_class).filter(
                        getattr(model_class, id_field) == record_id
                    ).update(update_data)
                    updated_count += result

            execution_time = time.time() - start_time
            self.performance_metrics['batch_update'].append({
                'count': len(updates),
                'updated': updated_count,
                'execution_time': execution_time,
                'timestamp': datetime.now()
            })

            logger.info(f"Batch updated {updated_count} {model_class.__name__} records in {execution_time:.2f}s")
            return updated_count

        except Exception as e:
            logger.error(f"Batch update failed for {model_class.__name__}: {e}")
            raise

    def batch_delete(self, db: Session, model_class: T, ids: List[Any],
                    id_field: str = 'id') -> int:
        """
        Perform batch delete operation.

        Args:
            db: Database session
            model_class: SQLAlchemy model class
            ids: List of IDs to delete
            id_field: Name of the ID field

        Returns:
            Number of deleted records
        """
        start_time = time.time()
        deleted_count = 0

        try:
            # Process in batches
            for i in range(0, len(ids), self.batch_size):
                batch_ids = ids[i:i + self.batch_size]

                # Perform bulk delete
                result = db.query(model_class).filter(
                    getattr(model_class, id_field).in_(batch_ids)
                ).delete(synchronize_session=False)
                deleted_count += result

            execution_time = time.time() - start_time
            self.performance_metrics['batch_delete'].append({
                'count': len(ids),
                'deleted': deleted_count,
                'execution_time': execution_time,
                'timestamp': datetime.now()
            })

            logger.info(f"Batch deleted {deleted_count} {model_class.__name__} records in {execution_time:.2f}s")
            return deleted_count

        except Exception as e:
            logger.error(f"Batch delete failed for {model_class.__name__}: {e}")
            raise

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get batch operation performance metrics."""
        metrics = {}

        for operation, data in self.performance_metrics.items():
            if data:
                total_records = sum(item['count'] for item in data)
                total_time = sum(item['execution_time'] for item in data)
                avg_time = total_time / len(data) if data else 0

                metrics[operation] = {
                    'total_operations': len(data),
                    'total_records': total_records,
                    'total_time': total_time,
                    'average_time': avg_time,
                    'records_per_second': total_records / total_time if total_time > 0 else 0
                }

        return metrics


class QueryCacheManager:
    """
    Manager for database query caching to improve performance.

    Provides caching for frequently executed queries with automatic
    cache invalidation and performance monitoring.
    """

    def __init__(self, default_ttl: int = 300):
        self.default_ttl = default_ttl
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_prefix = "db_query_cache:"

    def _generate_cache_key(self, query: str, params: Dict[str, Any] = None) -> str:
        """Generate cache key for query and parameters."""
        key_data = {
            'query': query,
            'params': params or {}
        }
        key_hash = hash(json.dumps(key_data, sort_keys=True))
        return f"{self.cache_prefix}{key_hash}"

    def get_cached_result(self, query: str, params: Dict[str, Any] = None) -> Optional[Any]:
        """Get cached query result."""
        if not redis_client:
            return None

        cache_key = self._generate_cache_key(query, params)

        try:
            cached_data = redis_client.get(cache_key)
            if cached_data:
                self.cache_hits += 1
                return json.loads(cached_data)
            else:
                self.cache_misses += 1
                return None
        except Exception as e:
            logger.warning(f"Cache retrieval error: {e}")
            return None

    def cache_result(self, query: str, result: Any, params: Dict[str, Any] = None,
                    ttl: Optional[int] = None) -> bool:
        """Cache query result."""
        if not redis_client:
            return False

        cache_key = self._generate_cache_key(query, params)
        ttl = ttl or self.default_ttl

        try:
            serialized_result = json.dumps(result, default=str)
            redis_client.setex(cache_key, ttl, serialized_result)
            return True
        except Exception as e:
            logger.warning(f"Cache storage error: {e}")
            return False

    def invalidate_cache_pattern(self, pattern: str) -> int:
        """Invalidate cache entries matching pattern."""
        if not redis_client:
            return 0

        try:
            keys = redis_client.keys(f"{self.cache_prefix}{pattern}")
            if keys:
                return redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Cache invalidation error: {e}")
            return 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'total_requests': total_requests,
            'hit_rate_percent': round(hit_rate, 2)
        }


# Global instances
batch_manager = BatchOperationManager()
query_cache = QueryCacheManager()


# Event listener for query performance monitoring
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query start time."""
    context._query_start_time = datetime.now()


@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query execution time."""
    if hasattr(context, '_query_start_time'):
        execution_time = (datetime.now() - context._query_start_time).total_seconds()

        # Log slow queries
        if execution_time > 1.0:  # 1 second threshold
            logger.warning(f"Slow query detected: {execution_time:.2f}s - {statement[:100]}...")

        # Update pool manager statistics
        try:
            from .optimization import pool_manager
            if hasattr(pool_manager, 'record_query_time'):
                query_type = "dashboard" if "dashboard" in statement.lower() else "other"
                pool_manager.record_query_time(execution_time, query_type)
        except ImportError:
            pass  # optimization module may not be available
