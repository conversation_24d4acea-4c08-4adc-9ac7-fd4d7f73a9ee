"""
Enhanced Resource Tracking System for Phase 2 Memory Leak Resolution.

This module provides comprehensive resource tracking with:
- Long-lived object tracking and monitoring
- Resource usage dashboards and metrics
- Automated memory cleanup triggers
- Resource lifecycle management
"""

import logging
import asyncio
import time
import weakref
import gc
from typing import Dict, Any, List, Optional, Set, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import threading
import json

logger = logging.getLogger(__name__)


class ResourceType(str, Enum):
    """Types of resources that can be tracked."""
    MEMORY_OBJECT = "memory_object"
    FILE_HANDLE = "file_handle"
    DATABASE_CONNECTION = "database_connection"
    NETWORK_CONNECTION = "network_connection"
    THREAD = "thread"
    ASYNC_TASK = "async_task"
    CACHE_ENTRY = "cache_entry"
    TEMPORARY_FILE = "temporary_file"
    AGENT_INSTANCE = "agent_instance"
    CONVERSATION = "conversation"


class ResourceStatus(str, Enum):
    """Status of tracked resources."""
    ACTIVE = "active"
    IDLE = "idle"
    CLEANUP_PENDING = "cleanup_pending"
    CLEANED_UP = "cleaned_up"
    LEAKED = "leaked"


@dataclass
class ResourceInfo:
    """Information about a tracked resource."""
    resource_id: str
    resource_type: ResourceType
    creation_time: datetime
    last_accessed: datetime
    access_count: int = 0
    status: ResourceStatus = ResourceStatus.ACTIVE
    metadata: Dict[str, Any] = field(default_factory=dict)
    cleanup_callback: Optional[Callable] = None
    size_bytes: int = 0
    tags: Set[str] = field(default_factory=set)


@dataclass
class ResourceUsageStats:
    """Statistics about resource usage."""
    total_resources: int = 0
    active_resources: int = 0
    idle_resources: int = 0
    leaked_resources: int = 0
    total_memory_bytes: int = 0
    cleanup_triggers: int = 0
    resources_by_type: Dict[ResourceType, int] = field(default_factory=dict)
    avg_resource_age_seconds: float = 0.0
    peak_resource_count: int = 0


class EnhancedResourceTracker:
    """
    Enhanced resource tracker with comprehensive monitoring,
    automated cleanup, and dashboard capabilities.
    """
    
    def __init__(self,
                 cleanup_interval: int = 300,  # 5 minutes
                 max_idle_time: int = 1800,    # 30 minutes
                 max_resource_age: int = 7200,  # 2 hours
                 memory_threshold_mb: float = 500.0):
        """Initialize the enhanced resource tracker."""
        self.logger = logging.getLogger(__name__)
        self.cleanup_interval = cleanup_interval
        self.max_idle_time = max_idle_time
        self.max_resource_age = max_resource_age
        self.memory_threshold_mb = memory_threshold_mb
        
        # Resource tracking
        self.tracked_resources: Dict[str, ResourceInfo] = {}
        self.resource_weak_refs: Dict[str, weakref.ref] = {}
        self.resource_stats = ResourceUsageStats()
        
        # Cleanup triggers
        self.cleanup_triggers: List[Callable[[], bool]] = []
        self.cleanup_callbacks: List[Callable[[List[str]], None]] = []
        
        # Dashboard data
        self.usage_history: deque = deque(maxlen=288)  # 24 hours of 5-minute intervals
        self.cleanup_history: deque = deque(maxlen=100)
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self._is_shutting_down = False
        self._monitoring_enabled = True
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Performance tracking
        self._last_cleanup_time = datetime.now()
        self._cleanup_count = 0
        
        # Start monitoring
        self._start_monitoring()
        
        self.logger.info("Enhanced resource tracker initialized")
    
    def _start_monitoring(self):
        """Start background monitoring tasks."""
        try:
            # Resource monitoring task
            task = asyncio.create_task(self._resource_monitoring_task())
            self._background_tasks.append(task)
            
            # Cleanup task
            task = asyncio.create_task(self._cleanup_task())
            self._background_tasks.append(task)
            
            # Statistics collection task
            task = asyncio.create_task(self._statistics_collection_task())
            self._background_tasks.append(task)
            
            # Dashboard data update task
            task = asyncio.create_task(self._dashboard_update_task())
            self._background_tasks.append(task)
            
            self.logger.info(f"Started {len(self._background_tasks)} resource monitoring tasks")
            
        except Exception as e:
            self.logger.error(f"Failed to start resource monitoring: {e}")
            raise
    
    async def _resource_monitoring_task(self):
        """Background task to monitor resource usage."""
        self.logger.info("Starting resource monitoring task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=60  # Check every minute
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._monitor_resources()
                    
            except asyncio.CancelledError:
                self.logger.info("Resource monitoring task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in resource monitoring: {e}")
                
        self.logger.info("Resource monitoring task finished")
    
    async def _cleanup_task(self):
        """Background task for automated resource cleanup."""
        self.logger.info("Starting resource cleanup task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=self.cleanup_interval
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._perform_automated_cleanup()
                    
            except asyncio.CancelledError:
                self.logger.info("Resource cleanup task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in resource cleanup: {e}")
                
        self.logger.info("Resource cleanup task finished")
    
    async def _statistics_collection_task(self):
        """Background task to collect resource statistics."""
        self.logger.info("Starting statistics collection task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=300  # Collect every 5 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._collect_statistics()
                    
            except asyncio.CancelledError:
                self.logger.info("Statistics collection task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in statistics collection: {e}")
                
        self.logger.info("Statistics collection task finished")
    
    async def _dashboard_update_task(self):
        """Background task to update dashboard data."""
        self.logger.info("Starting dashboard update task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=300  # Update every 5 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._update_dashboard_data()
                    
            except asyncio.CancelledError:
                self.logger.info("Dashboard update task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in dashboard update: {e}")
                
        self.logger.info("Dashboard update task finished")
    
    async def _monitor_resources(self):
        """Monitor tracked resources for issues."""
        try:
            current_time = datetime.now()
            
            with self._lock:
                # Check for leaked resources (weak refs that are None)
                leaked_resources = []
                for resource_id, weak_ref in list(self.resource_weak_refs.items()):
                    if weak_ref() is None and resource_id in self.tracked_resources:
                        resource_info = self.tracked_resources[resource_id]
                        if resource_info.status != ResourceStatus.CLEANED_UP:
                            resource_info.status = ResourceStatus.LEAKED
                            leaked_resources.append(resource_id)
                
                # Check for idle resources
                idle_resources = []
                for resource_id, resource_info in self.tracked_resources.items():
                    if resource_info.status == ResourceStatus.ACTIVE:
                        idle_time = (current_time - resource_info.last_accessed).total_seconds()
                        if idle_time > self.max_idle_time:
                            resource_info.status = ResourceStatus.IDLE
                            idle_resources.append(resource_id)
                
                # Check for old resources
                old_resources = []
                for resource_id, resource_info in self.tracked_resources.items():
                    age = (current_time - resource_info.creation_time).total_seconds()
                    if age > self.max_resource_age:
                        old_resources.append(resource_id)
                
                # Log findings
                if leaked_resources:
                    self.logger.warning(f"Detected {len(leaked_resources)} leaked resources")
                if idle_resources:
                    self.logger.debug(f"Detected {len(idle_resources)} idle resources")
                if old_resources:
                    self.logger.info(f"Detected {len(old_resources)} old resources")
                
        except Exception as e:
            self.logger.error(f"Error monitoring resources: {e}")
    
    async def _perform_automated_cleanup(self):
        """Perform automated cleanup of resources."""
        try:
            current_time = datetime.now()
            cleanup_candidates = []
            
            with self._lock:
                # Find resources that need cleanup
                for resource_id, resource_info in list(self.tracked_resources.items()):
                    should_cleanup = False
                    
                    # Check if resource is leaked
                    if resource_info.status == ResourceStatus.LEAKED:
                        should_cleanup = True
                    
                    # Check if resource is too old
                    age = (current_time - resource_info.creation_time).total_seconds()
                    if age > self.max_resource_age:
                        should_cleanup = True
                    
                    # Check if resource has been idle too long
                    idle_time = (current_time - resource_info.last_accessed).total_seconds()
                    if (resource_info.status == ResourceStatus.IDLE and 
                        idle_time > self.max_idle_time * 2):  # Double the idle time for cleanup
                        should_cleanup = True
                    
                    if should_cleanup:
                        cleanup_candidates.append(resource_id)
                
                # Check cleanup triggers
                for trigger in self.cleanup_triggers:
                    try:
                        if trigger():
                            self.logger.info("Cleanup trigger activated, performing cleanup")
                            # Add all idle resources to cleanup candidates
                            for resource_id, resource_info in self.tracked_resources.items():
                                if (resource_info.status == ResourceStatus.IDLE and 
                                    resource_id not in cleanup_candidates):
                                    cleanup_candidates.append(resource_id)
                            break
                    except Exception as e:
                        self.logger.error(f"Error in cleanup trigger: {e}")
                
                # Perform cleanup
                cleaned_resources = []
                for resource_id in cleanup_candidates:
                    if await self._cleanup_resource(resource_id):
                        cleaned_resources.append(resource_id)
                
                if cleaned_resources:
                    self._cleanup_count += len(cleaned_resources)
                    self._last_cleanup_time = current_time
                    
                    # Record cleanup event
                    cleanup_event = {
                        'timestamp': current_time.isoformat(),
                        'resources_cleaned': len(cleaned_resources),
                        'resource_ids': cleaned_resources[:10],  # Limit to first 10
                        'trigger': 'automated'
                    }
                    self.cleanup_history.append(cleanup_event)
                    
                    # Notify callbacks
                    for callback in self.cleanup_callbacks:
                        try:
                            callback(cleaned_resources)
                        except Exception as e:
                            self.logger.error(f"Error in cleanup callback: {e}")
                    
                    self.logger.info(f"Automated cleanup completed: {len(cleaned_resources)} resources cleaned")
                
        except Exception as e:
            self.logger.error(f"Error in automated cleanup: {e}")
    
    async def _cleanup_resource(self, resource_id: str) -> bool:
        """Clean up a specific resource."""
        try:
            if resource_id not in self.tracked_resources:
                return False
            
            resource_info = self.tracked_resources[resource_id]
            
            # Call cleanup callback if available
            if resource_info.cleanup_callback:
                try:
                    if asyncio.iscoroutinefunction(resource_info.cleanup_callback):
                        await resource_info.cleanup_callback()
                    else:
                        resource_info.cleanup_callback()
                except Exception as e:
                    self.logger.error(f"Error in cleanup callback for {resource_id}: {e}")
            
            # Update status
            resource_info.status = ResourceStatus.CLEANED_UP
            
            # Remove from tracking
            del self.tracked_resources[resource_id]
            if resource_id in self.resource_weak_refs:
                del self.resource_weak_refs[resource_id]
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error cleaning up resource {resource_id}: {e}")
            return False

    async def _collect_statistics(self):
        """Collect resource usage statistics."""
        try:
            current_time = datetime.now()

            with self._lock:
                # Reset stats
                self.resource_stats = ResourceUsageStats()

                # Count resources by status and type
                total_memory = 0
                total_age = 0

                for resource_info in self.tracked_resources.values():
                    self.resource_stats.total_resources += 1
                    total_memory += resource_info.size_bytes

                    age = (current_time - resource_info.creation_time).total_seconds()
                    total_age += age

                    # Count by status
                    if resource_info.status == ResourceStatus.ACTIVE:
                        self.resource_stats.active_resources += 1
                    elif resource_info.status == ResourceStatus.IDLE:
                        self.resource_stats.idle_resources += 1
                    elif resource_info.status == ResourceStatus.LEAKED:
                        self.resource_stats.leaked_resources += 1

                    # Count by type
                    resource_type = resource_info.resource_type
                    self.resource_stats.resources_by_type[resource_type] = (
                        self.resource_stats.resources_by_type.get(resource_type, 0) + 1
                    )

                # Calculate averages
                if self.resource_stats.total_resources > 0:
                    self.resource_stats.avg_resource_age_seconds = (
                        total_age / self.resource_stats.total_resources
                    )

                self.resource_stats.total_memory_bytes = total_memory
                self.resource_stats.cleanup_triggers = self._cleanup_count

                # Update peak count
                if self.resource_stats.total_resources > self.resource_stats.peak_resource_count:
                    self.resource_stats.peak_resource_count = self.resource_stats.total_resources

        except Exception as e:
            self.logger.error(f"Error collecting statistics: {e}")

    async def _update_dashboard_data(self):
        """Update dashboard data for monitoring."""
        try:
            current_time = datetime.now()

            # Create dashboard snapshot
            dashboard_data = {
                'timestamp': current_time.isoformat(),
                'total_resources': self.resource_stats.total_resources,
                'active_resources': self.resource_stats.active_resources,
                'idle_resources': self.resource_stats.idle_resources,
                'leaked_resources': self.resource_stats.leaked_resources,
                'memory_usage_mb': self.resource_stats.total_memory_bytes / 1024 / 1024,
                'avg_age_minutes': self.resource_stats.avg_resource_age_seconds / 60,
                'resources_by_type': dict(self.resource_stats.resources_by_type),
                'cleanup_count': self._cleanup_count
            }

            self.usage_history.append(dashboard_data)

        except Exception as e:
            self.logger.error(f"Error updating dashboard data: {e}")

    def track_resource(self,
                      resource: Any,
                      resource_type: ResourceType,
                      resource_id: Optional[str] = None,
                      cleanup_callback: Optional[Callable] = None,
                      size_bytes: int = 0,
                      metadata: Optional[Dict[str, Any]] = None,
                      tags: Optional[Set[str]] = None) -> str:
        """
        Track a resource for monitoring and cleanup.

        Args:
            resource: The resource object to track
            resource_type: Type of the resource
            resource_id: Optional custom resource ID
            cleanup_callback: Optional cleanup function
            size_bytes: Size of the resource in bytes
            metadata: Optional metadata
            tags: Optional tags for categorization

        Returns:
            Resource ID for tracking
        """
        try:
            with self._lock:
                # Generate resource ID if not provided
                if resource_id is None:
                    resource_id = f"{resource_type}_{id(resource)}_{int(time.time())}"

                # Create resource info
                resource_info = ResourceInfo(
                    resource_id=resource_id,
                    resource_type=resource_type,
                    creation_time=datetime.now(),
                    last_accessed=datetime.now(),
                    cleanup_callback=cleanup_callback,
                    size_bytes=size_bytes,
                    metadata=metadata or {},
                    tags=tags or set()
                )

                # Store resource info
                self.tracked_resources[resource_id] = resource_info

                # Create weak reference for leak detection
                def cleanup_weak_ref(ref):
                    if resource_id in self.resource_weak_refs:
                        del self.resource_weak_refs[resource_id]

                self.resource_weak_refs[resource_id] = weakref.ref(resource, cleanup_weak_ref)

                self.logger.debug(f"Started tracking resource: {resource_id} ({resource_type})")
                return resource_id

        except Exception as e:
            self.logger.error(f"Error tracking resource: {e}")
            return ""

    def access_resource(self, resource_id: str) -> bool:
        """
        Mark a resource as accessed (updates last_accessed time).

        Args:
            resource_id: ID of the resource

        Returns:
            True if resource was found and updated
        """
        try:
            with self._lock:
                if resource_id in self.tracked_resources:
                    resource_info = self.tracked_resources[resource_id]
                    resource_info.last_accessed = datetime.now()
                    resource_info.access_count += 1

                    # Update status if it was idle
                    if resource_info.status == ResourceStatus.IDLE:
                        resource_info.status = ResourceStatus.ACTIVE

                    return True
                return False

        except Exception as e:
            self.logger.error(f"Error accessing resource {resource_id}: {e}")
            return False

    def untrack_resource(self, resource_id: str) -> bool:
        """
        Stop tracking a resource.

        Args:
            resource_id: ID of the resource to untrack

        Returns:
            True if resource was found and removed
        """
        try:
            with self._lock:
                if resource_id in self.tracked_resources:
                    del self.tracked_resources[resource_id]

                if resource_id in self.resource_weak_refs:
                    del self.resource_weak_refs[resource_id]

                self.logger.debug(f"Stopped tracking resource: {resource_id}")
                return True

        except Exception as e:
            self.logger.error(f"Error untracking resource {resource_id}: {e}")
            return False

    def add_cleanup_trigger(self, trigger: Callable[[], bool]) -> None:
        """
        Add a cleanup trigger function.

        Args:
            trigger: Function that returns True when cleanup should be triggered
        """
        self.cleanup_triggers.append(trigger)

    def add_cleanup_callback(self, callback: Callable[[List[str]], None]) -> None:
        """
        Add a callback to be called after cleanup operations.

        Args:
            callback: Function to call with list of cleaned resource IDs
        """
        self.cleanup_callbacks.append(callback)

    def get_resource_info(self, resource_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific resource."""
        try:
            with self._lock:
                if resource_id in self.tracked_resources:
                    resource_info = self.tracked_resources[resource_id]
                    return {
                        'resource_id': resource_info.resource_id,
                        'resource_type': resource_info.resource_type.value,
                        'creation_time': resource_info.creation_time.isoformat(),
                        'last_accessed': resource_info.last_accessed.isoformat(),
                        'access_count': resource_info.access_count,
                        'status': resource_info.status.value,
                        'size_bytes': resource_info.size_bytes,
                        'metadata': resource_info.metadata,
                        'tags': list(resource_info.tags),
                        'age_seconds': (datetime.now() - resource_info.creation_time).total_seconds()
                    }
                return None

        except Exception as e:
            self.logger.error(f"Error getting resource info for {resource_id}: {e}")
            return None

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get dashboard data for monitoring interface."""
        try:
            with self._lock:
                return {
                    'current_stats': {
                        'total_resources': self.resource_stats.total_resources,
                        'active_resources': self.resource_stats.active_resources,
                        'idle_resources': self.resource_stats.idle_resources,
                        'leaked_resources': self.resource_stats.leaked_resources,
                        'memory_usage_mb': self.resource_stats.total_memory_bytes / 1024 / 1024,
                        'avg_age_minutes': self.resource_stats.avg_resource_age_seconds / 60,
                        'peak_resource_count': self.resource_stats.peak_resource_count,
                        'cleanup_count': self._cleanup_count,
                        'resources_by_type': dict(self.resource_stats.resources_by_type)
                    },
                    'usage_history': list(self.usage_history),
                    'cleanup_history': list(self.cleanup_history),
                    'monitoring_enabled': self._monitoring_enabled
                }

        except Exception as e:
            self.logger.error(f"Error getting dashboard data: {e}")
            return {}

    def get_resource_list(self,
                         resource_type: Optional[ResourceType] = None,
                         status: Optional[ResourceStatus] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """Get list of tracked resources with optional filtering."""
        try:
            with self._lock:
                resources = []

                for resource_info in self.tracked_resources.values():
                    # Apply filters
                    if resource_type and resource_info.resource_type != resource_type:
                        continue
                    if status and resource_info.status != status:
                        continue

                    resources.append({
                        'resource_id': resource_info.resource_id,
                        'resource_type': resource_info.resource_type.value,
                        'status': resource_info.status.value,
                        'creation_time': resource_info.creation_time.isoformat(),
                        'last_accessed': resource_info.last_accessed.isoformat(),
                        'access_count': resource_info.access_count,
                        'size_bytes': resource_info.size_bytes,
                        'age_seconds': (datetime.now() - resource_info.creation_time).total_seconds(),
                        'tags': list(resource_info.tags)
                    })

                # Sort by creation time (newest first) and limit
                resources.sort(key=lambda x: x['creation_time'], reverse=True)
                return resources[:limit]

        except Exception as e:
            self.logger.error(f"Error getting resource list: {e}")
            return []

    async def force_cleanup(self, resource_type: Optional[ResourceType] = None) -> int:
        """
        Force cleanup of resources.

        Args:
            resource_type: Optional resource type to filter by

        Returns:
            Number of resources cleaned up
        """
        try:
            cleanup_candidates = []

            with self._lock:
                for resource_id, resource_info in list(self.tracked_resources.items()):
                    if resource_type is None or resource_info.resource_type == resource_type:
                        cleanup_candidates.append(resource_id)

            cleaned_count = 0
            for resource_id in cleanup_candidates:
                if await self._cleanup_resource(resource_id):
                    cleaned_count += 1

            if cleaned_count > 0:
                self.logger.info(f"Force cleanup completed: {cleaned_count} resources cleaned")

                # Record cleanup event
                cleanup_event = {
                    'timestamp': datetime.now().isoformat(),
                    'resources_cleaned': cleaned_count,
                    'resource_ids': cleanup_candidates[:10],
                    'trigger': 'manual'
                }
                self.cleanup_history.append(cleanup_event)

            return cleaned_count

        except Exception as e:
            self.logger.error(f"Error in force cleanup: {e}")
            return 0

    def enable_monitoring(self) -> None:
        """Enable resource monitoring."""
        self._monitoring_enabled = True
        self.logger.info("Resource monitoring enabled")

    def disable_monitoring(self) -> None:
        """Disable resource monitoring."""
        self._monitoring_enabled = False
        self.logger.info("Resource monitoring disabled")

    async def shutdown(self, timeout: float = 30.0) -> None:
        """Gracefully shutdown the resource tracker."""
        self.logger.info("Starting resource tracker shutdown...")

        try:
            # Set shutdown flag
            self._is_shutting_down = True
            self._shutdown_event.set()

            # Wait for background tasks to complete
            if self._background_tasks:
                self.logger.info(f"Waiting for {len(self._background_tasks)} monitoring tasks to complete...")

                try:
                    await asyncio.wait_for(
                        asyncio.gather(*self._background_tasks, return_exceptions=True),
                        timeout=timeout
                    )
                    self.logger.info("All monitoring tasks completed gracefully")
                except asyncio.TimeoutError:
                    self.logger.warning(f"Monitoring tasks did not complete within {timeout}s, cancelling...")

                    # Cancel remaining tasks
                    for task in self._background_tasks:
                        if not task.done():
                            task.cancel()

                    # Wait a bit more for cancellation
                    try:
                        await asyncio.wait_for(
                            asyncio.gather(*self._background_tasks, return_exceptions=True),
                            timeout=5.0
                        )
                    except asyncio.TimeoutError:
                        self.logger.error("Some monitoring tasks failed to cancel properly")

            # Cleanup all remaining resources
            cleanup_count = await self.force_cleanup()

            # Clear data structures
            with self._lock:
                self.tracked_resources.clear()
                self.resource_weak_refs.clear()
                self.cleanup_triggers.clear()
                self.cleanup_callbacks.clear()
                self.usage_history.clear()
                self.cleanup_history.clear()

            self.logger.info(f"Resource tracker shutdown completed: {cleanup_count} resources cleaned")

        except Exception as e:
            self.logger.error(f"Error during resource tracker shutdown: {e}")
            raise


# Global resource tracker instance (lazy initialization)
_resource_tracker: Optional[EnhancedResourceTracker] = None


def get_resource_tracker(**kwargs) -> EnhancedResourceTracker:
    """Get the global resource tracker instance (lazy initialization)."""
    global _resource_tracker

    if _resource_tracker is None:
        _resource_tracker = EnhancedResourceTracker(**kwargs)

    return _resource_tracker


def track_resource(resource: Any,
                  resource_type: ResourceType,
                  **kwargs) -> str:
    """Track a resource using the global tracker."""
    tracker = get_resource_tracker()
    return tracker.track_resource(resource, resource_type, **kwargs)
