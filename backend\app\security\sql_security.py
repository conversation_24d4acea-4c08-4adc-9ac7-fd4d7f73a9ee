"""
SQL Security utilities for secure database operations.

This module provides utilities for secure SQL execution, injection protection,
and database query auditing.
"""

import logging
import time
import uuid
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum
from dataclasses import dataclass

from sqlalchemy import text
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)


class QueryType(Enum):
    """Types of SQL queries for security classification."""
    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    DDL = "DDL"  # Data Definition Language (CREATE, ALTER, DROP)
    ADMIN = "ADMIN"  # Administrative commands (SET, SHOW, etc.)


@dataclass
class QueryAuditLog:
    """Audit log entry for database queries."""
    query_id: str
    query_type: QueryType
    query_text: str
    parameters: Dict[str, Any]
    execution_time: float
    timestamp: datetime
    user_id: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None


class SecureSQLExecutor:
    """Secure SQL executor with injection protection and auditing."""
    
    def __init__(self):
        self.audit_logs: List[QueryAuditLog] = []
        self.max_audit_logs = 1000
        
        # Allowed administrative commands for optimization
        self.allowed_admin_commands = {
            'SET plan_cache_mode',
            'SET work_mem',
            'SET max_parallel_workers_per_gather',
            'SET random_page_cost',
            'SET effective_cache_size',
            'SET enable_seqscan',
            'SET enable_indexscan',
            'SET enable_bitmapscan',
            'SET enable_hashjoin',
            'SET enable_mergejoin',
            'SET enable_nestloop',
            'SET shared_preload_libraries',
            'SET log_statement',
            'SET log_min_duration_statement'
        }
    
    def _generate_query_id(self) -> str:
        """Generate unique query ID for auditing."""
        return f"sql_{uuid.uuid4().hex[:8]}"
    
    def _classify_query(self, query_text: str) -> QueryType:
        """Classify query type for security purposes."""
        query_upper = query_text.strip().upper()
        
        if query_upper.startswith('SELECT'):
            return QueryType.SELECT
        elif query_upper.startswith('INSERT'):
            return QueryType.INSERT
        elif query_upper.startswith('UPDATE'):
            return QueryType.UPDATE
        elif query_upper.startswith('DELETE'):
            return QueryType.DELETE
        elif query_upper.startswith(('CREATE', 'ALTER', 'DROP')):
            return QueryType.DDL
        elif query_upper.startswith(('SET', 'SHOW', 'RESET')):
            return QueryType.ADMIN
        else:
            return QueryType.ADMIN  # Default to admin for safety
    
    def _validate_admin_command(self, query_text: str) -> bool:
        """Validate that admin command is allowed."""
        query_upper = query_text.strip().upper()
        
        # Check if it's an allowed SET command
        for allowed_cmd in self.allowed_admin_commands:
            if query_upper.startswith(allowed_cmd.upper()):
                return True
        
        return False
    
    def _sanitize_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize parameters to prevent injection."""
        sanitized = {}
        
        for key, value in parameters.items():
            if isinstance(value, str):
                # Remove dangerous SQL keywords and characters
                sanitized_value = value.replace("'", "''")  # Escape single quotes
                sanitized_value = sanitized_value.replace(";", "")  # Remove semicolons
                sanitized_value = sanitized_value.replace("--", "")  # Remove SQL comments
                sanitized[key] = sanitized_value
            else:
                sanitized[key] = value
        
        return sanitized
    
    def _audit_query(self, query_id: str, query_type: QueryType, query_text: str,
                    parameters: Dict[str, Any], execution_time: float,
                    success: bool = True, error_message: str = None,
                    user_id: str = None):
        """Audit database query execution."""
        audit_entry = QueryAuditLog(
            query_id=query_id,
            query_type=query_type,
            query_text=query_text,
            parameters=parameters,
            execution_time=execution_time,
            timestamp=datetime.now(),
            user_id=user_id,
            success=success,
            error_message=error_message
        )
        
        self.audit_logs.append(audit_entry)
        
        # Maintain audit log size
        if len(self.audit_logs) > self.max_audit_logs:
            self.audit_logs = self.audit_logs[-self.max_audit_logs:]
        
        # Log audit entry
        log_level = logging.INFO if success else logging.ERROR
        logger.log(
            log_level,
            f"SQL Query [{query_id}]: {query_type.value} - "
            f"Success: {success} - Time: {execution_time:.3f}s"
        )
        
        if error_message:
            logger.error(f"SQL Error [{query_id}]: {error_message}")
    
    async def execute_secure(self, session: Session, query_text: str,
                           parameters: Dict[str, Any] = None,
                           user_id: str = None) -> Any:
        """Execute SQL query with security checks and auditing."""
        query_id = self._generate_query_id()
        start_time = time.time()
        parameters = parameters or {}
        
        try:
            # Classify query type
            query_type = self._classify_query(query_text)
            
            # Security validation
            if query_type == QueryType.DDL:
                raise SecurityError("DDL operations not allowed through this interface")
            
            if query_type == QueryType.ADMIN:
                if not self._validate_admin_command(query_text):
                    raise SecurityError(f"Admin command not allowed: {query_text}")
            
            # Sanitize parameters
            sanitized_params = self._sanitize_parameters(parameters)
            
            # Execute query
            if sanitized_params:
                result = await session.execute(text(query_text), sanitized_params)
            else:
                result = await session.execute(text(query_text))
            
            execution_time = time.time() - start_time
            
            # Audit successful execution
            self._audit_query(
                query_id, query_type, query_text, sanitized_params,
                execution_time, success=True, user_id=user_id
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_message = str(e)
            
            # Audit failed execution
            self._audit_query(
                query_id, self._classify_query(query_text), query_text,
                parameters, execution_time, success=False,
                error_message=error_message, user_id=user_id
            )
            
            # Re-raise with security context
            raise SecurityError(f"SQL execution failed [{query_id}]: {error_message}")
    
    def get_audit_logs(self, limit: int = 100) -> List[QueryAuditLog]:
        """Get recent audit logs."""
        return self.audit_logs[-limit:]
    
    def get_security_metrics(self) -> Dict[str, Any]:
        """Get security metrics for monitoring."""
        total_queries = len(self.audit_logs)
        failed_queries = sum(1 for log in self.audit_logs if not log.success)
        
        query_types = {}
        for log in self.audit_logs:
            query_types[log.query_type.value] = query_types.get(log.query_type.value, 0) + 1
        
        return {
            "total_queries": total_queries,
            "failed_queries": failed_queries,
            "success_rate": (total_queries - failed_queries) / total_queries if total_queries > 0 else 0,
            "query_types": query_types,
            "last_audit_time": self.audit_logs[-1].timestamp if self.audit_logs else None
        }


class SecurityError(Exception):
    """Custom exception for SQL security violations."""
    pass


# Global secure executor instance
secure_sql_executor = SecureSQLExecutor()


async def execute_secure_sql(session: Session, query_text: str,
                           parameters: Dict[str, Any] = None,
                           user_id: str = None) -> Any:
    """Execute SQL query securely with auditing."""
    return await secure_sql_executor.execute_secure(session, query_text, parameters, user_id)


def get_sql_audit_logs(limit: int = 100) -> List[QueryAuditLog]:
    """Get SQL audit logs."""
    return secure_sql_executor.get_audit_logs(limit)


def get_sql_security_metrics() -> Dict[str, Any]:
    """Get SQL security metrics."""
    return secure_sql_executor.get_security_metrics()
