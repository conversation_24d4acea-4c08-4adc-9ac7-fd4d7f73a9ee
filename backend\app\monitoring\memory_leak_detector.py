"""
Enhanced Memory Leak Detection System for Phase 2 Memory Leak Resolution.

This module provides comprehensive memory leak detection with:
- Memory profiling in production
- Memory usage alerts and notifications
- Object tracking and lifecycle monitoring
- Automatic leak detection and reporting
"""

import logging
import asyncio
import gc
import sys
import traceback
import weakref
from typing import Dict, Any, List, Optional, Set, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
import time

logger = logging.getLogger(__name__)


@dataclass
class MemorySnapshot:
    """Snapshot of memory usage at a point in time."""
    timestamp: datetime
    total_objects: int
    memory_usage_mb: float
    gc_stats: Dict[str, int]
    top_object_types: Dict[str, int]
    process_memory_mb: float = 0.0
    heap_size_mb: float = 0.0


@dataclass
class LeakAlert:
    """Memory leak alert information."""
    alert_id: str
    timestamp: datetime
    alert_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    memory_increase_mb: float
    object_count_increase: int
    suspected_objects: List[str]
    stack_trace: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ObjectTracker:
    """Tracks lifecycle of specific object types."""
    object_type: str
    creation_count: int = 0
    deletion_count: int = 0
    current_count: int = 0
    peak_count: int = 0
    total_memory_mb: float = 0.0
    creation_rate: float = 0.0  # objects per second
    deletion_rate: float = 0.0  # objects per second


class EnhancedMemoryLeakDetector:
    """
    Enhanced memory leak detector with production-ready monitoring,
    alerting, and automatic detection capabilities.
    """
    
    def __init__(self, 
                 snapshot_interval: int = 300,  # 5 minutes
                 alert_threshold_mb: float = 100.0,
                 max_snapshots: int = 288):  # 24 hours of 5-minute snapshots
        """Initialize the memory leak detector."""
        self.logger = logging.getLogger(__name__)
        self.snapshot_interval = snapshot_interval
        self.alert_threshold_mb = alert_threshold_mb
        self.max_snapshots = max_snapshots
        
        # Memory snapshots
        self.snapshots: deque[MemorySnapshot] = deque(maxlen=max_snapshots)
        
        # Object tracking
        self.object_trackers: Dict[str, ObjectTracker] = {}
        self.tracked_objects: Dict[int, weakref.ref] = {}
        self.object_creation_times: Dict[int, datetime] = {}
        
        # Leak detection
        self.leak_alerts: List[LeakAlert] = []
        self.alert_callbacks: List[Callable[[LeakAlert], None]] = []
        self.suspected_leaks: Set[str] = set()
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self._is_shutting_down = False
        self._monitoring_enabled = True
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Performance tracking
        self._last_gc_time = time.time()
        self._gc_frequency = deque(maxlen=100)
        
        # Start monitoring
        self._start_monitoring()
        
        self.logger.info("Enhanced memory leak detector initialized")
    
    def _start_monitoring(self):
        """Start background monitoring tasks."""
        try:
            # Memory snapshot task
            task = asyncio.create_task(self._memory_snapshot_task())
            self._background_tasks.append(task)
            
            # Leak detection task
            task = asyncio.create_task(self._leak_detection_task())
            self._background_tasks.append(task)
            
            # Object lifecycle monitoring task
            task = asyncio.create_task(self._object_monitoring_task())
            self._background_tasks.append(task)
            
            # GC monitoring task
            task = asyncio.create_task(self._gc_monitoring_task())
            self._background_tasks.append(task)
            
            self.logger.info(f"Started {len(self._background_tasks)} memory monitoring tasks")
            
        except Exception as e:
            self.logger.error(f"Failed to start memory monitoring: {e}")
            raise
    
    async def _memory_snapshot_task(self):
        """Background task to take periodic memory snapshots."""
        self.logger.info("Starting memory snapshot task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=self.snapshot_interval
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._take_memory_snapshot()
                    
            except asyncio.CancelledError:
                self.logger.info("Memory snapshot task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in memory snapshot task: {e}")
                
        self.logger.info("Memory snapshot task finished")
    
    async def _leak_detection_task(self):
        """Background task to detect memory leaks."""
        self.logger.info("Starting leak detection task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=600  # Check every 10 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._detect_memory_leaks()
                    
            except asyncio.CancelledError:
                self.logger.info("Leak detection task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in leak detection task: {e}")
                
        self.logger.info("Leak detection task finished")
    
    async def _object_monitoring_task(self):
        """Background task to monitor object lifecycles."""
        self.logger.info("Starting object monitoring task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=120  # Check every 2 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._monitor_object_lifecycles()
                    
            except asyncio.CancelledError:
                self.logger.info("Object monitoring task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in object monitoring task: {e}")
                
        self.logger.info("Object monitoring task finished")
    
    async def _gc_monitoring_task(self):
        """Background task to monitor garbage collection."""
        self.logger.info("Starting GC monitoring task")
        
        while not self._is_shutting_down and self._monitoring_enabled:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=60  # Check every minute
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._monitor_garbage_collection()
                    
            except asyncio.CancelledError:
                self.logger.info("GC monitoring task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in GC monitoring task: {e}")
                
        self.logger.info("GC monitoring task finished")
    
    async def _take_memory_snapshot(self):
        """Take a snapshot of current memory usage."""
        try:
            with self._lock:
                # Get object counts by type
                object_counts = defaultdict(int)
                for obj in gc.get_objects():
                    obj_type = type(obj).__name__
                    object_counts[obj_type] += 1
                
                # Get top object types
                top_objects = dict(sorted(
                    object_counts.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:20])
                
                # Get GC statistics
                gc_stats = {}
                for i, count in enumerate(gc.get_count()):
                    gc_stats[f'generation_{i}'] = count
                
                # Get process memory if available
                process_memory_mb = 0.0
                try:
                    import psutil
                    import os
                    process = psutil.Process(os.getpid())
                    process_memory_mb = process.memory_info().rss / 1024 / 1024
                except ImportError:
                    pass
                
                # Create snapshot
                snapshot = MemorySnapshot(
                    timestamp=datetime.now(),
                    total_objects=len(gc.get_objects()),
                    memory_usage_mb=sys.getsizeof(gc.get_objects()) / 1024 / 1024,
                    gc_stats=gc_stats,
                    top_object_types=top_objects,
                    process_memory_mb=process_memory_mb
                )
                
                self.snapshots.append(snapshot)
                
                self.logger.debug(
                    f"Memory snapshot taken - Objects: {snapshot.total_objects}, "
                    f"Memory: {snapshot.process_memory_mb:.1f}MB"
                )
                
        except Exception as e:
            self.logger.error(f"Error taking memory snapshot: {e}")
    
    async def _detect_memory_leaks(self):
        """Detect potential memory leaks by analyzing snapshots."""
        try:
            if len(self.snapshots) < 3:
                return  # Need at least 3 snapshots for trend analysis
            
            with self._lock:
                recent_snapshots = list(self.snapshots)[-10:]  # Last 10 snapshots
                
                # Analyze memory growth trend
                memory_values = [s.process_memory_mb for s in recent_snapshots if s.process_memory_mb > 0]
                if len(memory_values) >= 3:
                    memory_growth = memory_values[-1] - memory_values[0]
                    
                    if memory_growth > self.alert_threshold_mb:
                        await self._create_leak_alert(
                            alert_type="memory_growth",
                            severity="high" if memory_growth > 200 else "medium",
                            description=f"Sustained memory growth detected: {memory_growth:.1f}MB increase",
                            memory_increase_mb=memory_growth,
                            object_count_increase=0
                        )
                
                # Analyze object count growth
                object_values = [s.total_objects for s in recent_snapshots]
                if len(object_values) >= 3:
                    object_growth = object_values[-1] - object_values[0]
                    
                    if object_growth > 10000:  # 10k objects threshold
                        await self._create_leak_alert(
                            alert_type="object_growth",
                            severity="medium",
                            description=f"High object count growth: {object_growth} new objects",
                            memory_increase_mb=0.0,
                            object_count_increase=object_growth
                        )
                
                # Analyze specific object types
                await self._analyze_object_type_growth(recent_snapshots)
                
        except Exception as e:
            self.logger.error(f"Error detecting memory leaks: {e}")
    
    async def _analyze_object_type_growth(self, snapshots: List[MemorySnapshot]):
        """Analyze growth in specific object types."""
        try:
            if len(snapshots) < 2:
                return
            
            first_snapshot = snapshots[0]
            last_snapshot = snapshots[-1]
            
            # Compare object type counts
            for obj_type, current_count in last_snapshot.top_object_types.items():
                previous_count = first_snapshot.top_object_types.get(obj_type, 0)
                growth = current_count - previous_count
                
                # Check for significant growth
                if growth > 1000 and growth > previous_count * 0.5:  # 50% growth + 1000 objects
                    await self._create_leak_alert(
                        alert_type="object_type_growth",
                        severity="medium",
                        description=f"High growth in {obj_type} objects: {growth} new instances",
                        memory_increase_mb=0.0,
                        object_count_increase=growth,
                        suspected_objects=[obj_type]
                    )
                    
                    self.suspected_leaks.add(obj_type)
            
        except Exception as e:
            self.logger.error(f"Error analyzing object type growth: {e}")

    async def _monitor_object_lifecycles(self):
        """Monitor object creation and deletion patterns."""
        try:
            with self._lock:
                # Clean up dead weak references
                dead_refs = []
                for obj_id, weak_ref in list(self.tracked_objects.items()):
                    if weak_ref() is None:
                        dead_refs.append(obj_id)

                for obj_id in dead_refs:
                    del self.tracked_objects[obj_id]
                    if obj_id in self.object_creation_times:
                        del self.object_creation_times[obj_id]

                # Update object tracker statistics
                for tracker in self.object_trackers.values():
                    tracker.deletion_count += len([
                        obj_id for obj_id in dead_refs
                        if obj_id in self.object_creation_times
                    ])
                    tracker.current_count = tracker.creation_count - tracker.deletion_count

                    if tracker.current_count > tracker.peak_count:
                        tracker.peak_count = tracker.current_count

                if dead_refs:
                    self.logger.debug(f"Cleaned up {len(dead_refs)} dead object references")

        except Exception as e:
            self.logger.error(f"Error monitoring object lifecycles: {e}")

    async def _monitor_garbage_collection(self):
        """Monitor garbage collection frequency and effectiveness."""
        try:
            current_time = time.time()
            time_since_last_gc = current_time - self._last_gc_time

            # Record GC frequency
            self._gc_frequency.append(time_since_last_gc)
            self._last_gc_time = current_time

            # Check if GC is running too frequently (potential memory pressure)
            if len(self._gc_frequency) >= 10:
                avg_gc_interval = sum(self._gc_frequency) / len(self._gc_frequency)

                if avg_gc_interval < 30:  # GC running more than every 30 seconds
                    await self._create_leak_alert(
                        alert_type="high_gc_frequency",
                        severity="medium",
                        description=f"High GC frequency detected: avg interval {avg_gc_interval:.1f}s",
                        memory_increase_mb=0.0,
                        object_count_increase=0
                    )

            # Force garbage collection and measure effectiveness
            before_objects = len(gc.get_objects())
            collected = gc.collect()
            after_objects = len(gc.get_objects())

            objects_freed = before_objects - after_objects

            self.logger.debug(
                f"GC cycle completed - Collected: {collected}, "
                f"Objects freed: {objects_freed}, Remaining: {after_objects}"
            )

            # Check for ineffective GC (sign of potential leaks)
            if collected > 0 and objects_freed < 100:  # GC ran but freed very few objects
                await self._create_leak_alert(
                    alert_type="ineffective_gc",
                    severity="low",
                    description=f"GC collected {collected} but freed only {objects_freed} objects",
                    memory_increase_mb=0.0,
                    object_count_increase=0
                )

        except Exception as e:
            self.logger.error(f"Error monitoring garbage collection: {e}")

    async def _create_leak_alert(self,
                               alert_type: str,
                               severity: str,
                               description: str,
                               memory_increase_mb: float,
                               object_count_increase: int,
                               suspected_objects: Optional[List[str]] = None):
        """Create and process a memory leak alert."""
        try:
            alert = LeakAlert(
                alert_id=f"{alert_type}_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=alert_type,
                severity=severity,
                description=description,
                memory_increase_mb=memory_increase_mb,
                object_count_increase=object_count_increase,
                suspected_objects=suspected_objects or [],
                stack_trace=traceback.format_stack()[-5:] if severity in ['high', 'critical'] else None
            )

            self.leak_alerts.append(alert)

            # Keep only recent alerts (last 100)
            if len(self.leak_alerts) > 100:
                self.leak_alerts = self.leak_alerts[-100:]

            # Log alert
            self.logger.warning(
                f"Memory leak alert [{severity.upper()}]: {description} "
                f"(Memory: +{memory_increase_mb:.1f}MB, Objects: +{object_count_increase})"
            )

            # Trigger callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")

        except Exception as e:
            self.logger.error(f"Error creating leak alert: {e}")

    def track_object(self, obj: Any, object_type: Optional[str] = None) -> None:
        """
        Track an object for lifecycle monitoring.

        Args:
            obj: Object to track
            object_type: Optional object type name
        """
        try:
            with self._lock:
                obj_id = id(obj)
                obj_type = object_type or type(obj).__name__

                # Create weak reference
                def cleanup_callback(ref):
                    if obj_id in self.tracked_objects:
                        del self.tracked_objects[obj_id]
                    if obj_id in self.object_creation_times:
                        del self.object_creation_times[obj_id]

                self.tracked_objects[obj_id] = weakref.ref(obj, cleanup_callback)
                self.object_creation_times[obj_id] = datetime.now()

                # Update tracker
                if obj_type not in self.object_trackers:
                    self.object_trackers[obj_type] = ObjectTracker(object_type=obj_type)

                tracker = self.object_trackers[obj_type]
                tracker.creation_count += 1
                tracker.current_count = tracker.creation_count - tracker.deletion_count

                if tracker.current_count > tracker.peak_count:
                    tracker.peak_count = tracker.current_count

        except Exception as e:
            self.logger.error(f"Error tracking object: {e}")

    def add_alert_callback(self, callback: Callable[[LeakAlert], None]) -> None:
        """Add a callback function to be called when alerts are created."""
        self.alert_callbacks.append(callback)

    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics."""
        try:
            with self._lock:
                recent_snapshots = list(self.snapshots)[-10:] if self.snapshots else []

                stats = {
                    'total_snapshots': len(self.snapshots),
                    'recent_alerts': len([a for a in self.leak_alerts if
                                        (datetime.now() - a.timestamp).total_seconds() < 3600]),
                    'suspected_leaks': list(self.suspected_leaks),
                    'object_trackers': {
                        name: {
                            'creation_count': tracker.creation_count,
                            'deletion_count': tracker.deletion_count,
                            'current_count': tracker.current_count,
                            'peak_count': tracker.peak_count
                        }
                        for name, tracker in self.object_trackers.items()
                    },
                    'gc_frequency_avg': sum(self._gc_frequency) / len(self._gc_frequency) if self._gc_frequency else 0,
                    'monitoring_enabled': self._monitoring_enabled
                }

                if recent_snapshots:
                    latest = recent_snapshots[-1]
                    stats.update({
                        'latest_snapshot': {
                            'timestamp': latest.timestamp.isoformat(),
                            'total_objects': latest.total_objects,
                            'process_memory_mb': latest.process_memory_mb,
                            'top_object_types': latest.top_object_types
                        }
                    })

                    if len(recent_snapshots) >= 2:
                        first = recent_snapshots[0]
                        memory_trend = latest.process_memory_mb - first.process_memory_mb
                        object_trend = latest.total_objects - first.total_objects

                        stats.update({
                            'memory_trend_mb': memory_trend,
                            'object_trend': object_trend
                        })

                return stats

        except Exception as e:
            self.logger.error(f"Error getting memory statistics: {e}")
            return {}

    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent memory leak alerts."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_alerts = [
                alert for alert in self.leak_alerts
                if alert.timestamp >= cutoff_time
            ]

            return [
                {
                    'alert_id': alert.alert_id,
                    'timestamp': alert.timestamp.isoformat(),
                    'alert_type': alert.alert_type,
                    'severity': alert.severity,
                    'description': alert.description,
                    'memory_increase_mb': alert.memory_increase_mb,
                    'object_count_increase': alert.object_count_increase,
                    'suspected_objects': alert.suspected_objects
                }
                for alert in recent_alerts
            ]

        except Exception as e:
            self.logger.error(f"Error getting recent alerts: {e}")
            return []

    def enable_monitoring(self) -> None:
        """Enable memory monitoring."""
        self._monitoring_enabled = True
        self.logger.info("Memory monitoring enabled")

    def disable_monitoring(self) -> None:
        """Disable memory monitoring."""
        self._monitoring_enabled = False
        self.logger.info("Memory monitoring disabled")

    async def force_memory_analysis(self) -> Dict[str, Any]:
        """Force immediate memory analysis and return results."""
        try:
            await self._take_memory_snapshot()
            await self._detect_memory_leaks()
            await self._monitor_object_lifecycles()
            await self._monitor_garbage_collection()

            return self.get_memory_statistics()

        except Exception as e:
            self.logger.error(f"Error in force memory analysis: {e}")
            return {}

    async def shutdown(self, timeout: float = 30.0) -> None:
        """Gracefully shutdown the memory leak detector."""
        self.logger.info("Starting memory leak detector shutdown...")

        try:
            # Set shutdown flag
            self._is_shutting_down = True
            self._shutdown_event.set()

            # Wait for background tasks to complete
            if self._background_tasks:
                self.logger.info(f"Waiting for {len(self._background_tasks)} monitoring tasks to complete...")

                try:
                    await asyncio.wait_for(
                        asyncio.gather(*self._background_tasks, return_exceptions=True),
                        timeout=timeout
                    )
                    self.logger.info("All monitoring tasks completed gracefully")
                except asyncio.TimeoutError:
                    self.logger.warning(f"Monitoring tasks did not complete within {timeout}s, cancelling...")

                    # Cancel remaining tasks
                    for task in self._background_tasks:
                        if not task.done():
                            task.cancel()

                    # Wait a bit more for cancellation
                    try:
                        await asyncio.wait_for(
                            asyncio.gather(*self._background_tasks, return_exceptions=True),
                            timeout=5.0
                        )
                    except asyncio.TimeoutError:
                        self.logger.error("Some monitoring tasks failed to cancel properly")

            # Clear data structures
            with self._lock:
                self.snapshots.clear()
                self.tracked_objects.clear()
                self.object_creation_times.clear()
                self.object_trackers.clear()
                self.leak_alerts.clear()
                self.alert_callbacks.clear()
                self.suspected_leaks.clear()

            self.logger.info("Memory leak detector shutdown completed")

        except Exception as e:
            self.logger.error(f"Error during memory leak detector shutdown: {e}")
            raise


# Global memory leak detector instance (lazy initialization)
_memory_leak_detector: Optional[EnhancedMemoryLeakDetector] = None


def get_memory_leak_detector(**kwargs) -> EnhancedMemoryLeakDetector:
    """Get the global memory leak detector instance (lazy initialization)."""
    global _memory_leak_detector

    if _memory_leak_detector is None:
        _memory_leak_detector = EnhancedMemoryLeakDetector(**kwargs)

    return _memory_leak_detector


def track_object_for_leaks(obj: Any, object_type: Optional[str] = None) -> None:
    """Track an object for memory leak detection using the global detector."""
    detector = get_memory_leak_detector()
    detector.track_object(obj, object_type)
