"""
JWT Security utilities for secure token management.

This module provides utilities for secure JWT secret generation,
rotation, and environment-specific validation.
"""

import os
import secrets
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class JWTSecurityManager:
    """Manages JWT security including secret generation and rotation."""
    
    def __init__(self):
        self.secret_file_path = Path("backend/app/security/.jwt_secrets")
        self.rotation_interval_days = 30
        
    def generate_strong_secret(self, length: int = 64) -> str:
        """Generate a cryptographically strong secret key."""
        return secrets.token_urlsafe(length)
    
    def validate_secret_strength(self, secret: str) -> bool:
        """Validate that a secret meets security requirements."""
        if len(secret) < 32:
            logger.warning("JWT secret is too short (minimum 32 characters)")
            return False
        
        # Check for weak default secrets
        weak_patterns = [
            "your-secret-key",
            "development-only",
            "test-secret",
            "default-key",
            "changeme"
        ]
        
        secret_lower = secret.lower()
        for pattern in weak_patterns:
            if pattern in secret_lower:
                logger.error(f"JWT secret contains weak pattern: {pattern}")
                return False
        
        return True
    
    def get_environment_specific_secret(self) -> str:
        """Get or generate environment-specific JWT secret."""
        env = os.getenv("ENVIRONMENT", "development")
        secret_env_var = f"JWT_SECRET_KEY_{env.upper()}"
        
        # Try to get environment-specific secret first
        secret = os.getenv(secret_env_var)
        if secret and self.validate_secret_strength(secret):
            return secret
        
        # Fall back to generic JWT_SECRET_KEY
        secret = os.getenv("JWT_SECRET_KEY")
        if secret and self.validate_secret_strength(secret):
            return secret
        
        # Generate a new strong secret if none exists or is weak
        logger.warning("No strong JWT secret found, generating new one")
        new_secret = self.generate_strong_secret()
        
        # Log recommendation to set environment variable
        logger.info(f"Set environment variable {secret_env_var}={new_secret}")
        
        return new_secret
    
    def check_secret_rotation_needed(self) -> bool:
        """Check if JWT secret rotation is needed."""
        try:
            if not self.secret_file_path.exists():
                return True
            
            # Check file modification time
            mod_time = datetime.fromtimestamp(self.secret_file_path.stat().st_mtime)
            rotation_due = datetime.now() - timedelta(days=self.rotation_interval_days)
            
            return mod_time < rotation_due
        except Exception as e:
            logger.error(f"Error checking secret rotation: {e}")
            return True
    
    def rotate_secret(self) -> str:
        """Rotate JWT secret and return new secret."""
        new_secret = self.generate_strong_secret()
        
        try:
            # Ensure directory exists
            self.secret_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Store rotation metadata
            rotation_data = {
                "rotated_at": datetime.now().isoformat(),
                "environment": os.getenv("ENVIRONMENT", "development"),
                "secret_length": len(new_secret)
            }
            
            # Write metadata (not the actual secret for security)
            with open(self.secret_file_path, 'w') as f:
                import json
                json.dump(rotation_data, f, indent=2)
            
            logger.info(f"JWT secret rotated successfully at {rotation_data['rotated_at']}")
            
        except Exception as e:
            logger.error(f"Error during secret rotation: {e}")
        
        return new_secret
    
    def validate_environment_security(self) -> Dict[str, Any]:
        """Validate security configuration for current environment."""
        env = os.getenv("ENVIRONMENT", "development")
        validation_results = {
            "environment": env,
            "issues": [],
            "recommendations": [],
            "security_score": 100
        }
        
        # Check JWT secret
        secret = os.getenv("JWT_SECRET_KEY")
        if not secret or not self.validate_secret_strength(secret):
            validation_results["issues"].append("Weak or missing JWT secret")
            validation_results["recommendations"].append("Set strong JWT_SECRET_KEY environment variable")
            validation_results["security_score"] -= 30
        
        # Environment-specific checks
        if env == "production":
            # Production should have strict security
            if os.getenv("ENFORCE_HTTPS", "false").lower() != "true":
                validation_results["issues"].append("HTTPS not enforced in production")
                validation_results["recommendations"].append("Set ENFORCE_HTTPS=true")
                validation_results["security_score"] -= 20
            
            if os.getenv("RATE_LIMITING_ENABLED", "true").lower() != "true":
                validation_results["issues"].append("Rate limiting disabled in production")
                validation_results["recommendations"].append("Set RATE_LIMITING_ENABLED=true")
                validation_results["security_score"] -= 15
        
        elif env == "development":
            # Development should still have basic security
            if secret and "development-only" in secret.lower():
                validation_results["issues"].append("Using default development secret")
                validation_results["recommendations"].append("Generate unique development secret")
                validation_results["security_score"] -= 10
        
        return validation_results


def get_secure_jwt_secret() -> str:
    """Get a secure JWT secret for the current environment."""
    manager = JWTSecurityManager()
    return manager.get_environment_specific_secret()


def validate_jwt_security() -> Dict[str, Any]:
    """Validate JWT security configuration."""
    manager = JWTSecurityManager()
    return manager.validate_environment_security()


def rotate_jwt_secret_if_needed() -> Optional[str]:
    """Rotate JWT secret if rotation is needed."""
    manager = JWTSecurityManager()
    
    if manager.check_secret_rotation_needed():
        logger.info("JWT secret rotation needed, generating new secret")
        return manager.rotate_secret()
    
    return None
