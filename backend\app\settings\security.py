"""
Security configuration settings.

This module provides security-specific configuration with validation
for authentication, authorization, and security policies.
"""

import os
import logging
from typing import List, Optional
from pydantic import Field, field_validator, computed_field
from .base import BaseConfig

logger = logging.getLogger(__name__)


class SecurityConfig(BaseConfig):
    """Security configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    # JWT Configuration
    jwt_secret_key: str = Field(..., description="JWT secret key for token signing")
    jwt_algorithm: str = Field(default="HS256", description="JWT signing algorithm")
    access_token_expire_minutes: int = Field(
        default=30, ge=5, le=1440, description="Access token expiration in minutes"
    )
    refresh_token_expire_days: int = Field(
        default=7, ge=1, le=30, description="Refresh token expiration in days"
    )
    
    # Session Management
    max_refresh_count: int = Field(
        default=30, ge=1, le=100, description="Maximum token refresh count"
    )
    max_concurrent_sessions: int = Field(
        default=5, ge=1, le=20, description="Maximum concurrent sessions per user"
    )
    enforce_ip_validation: bool = Field(
        default=False, description="Enforce IP address validation for sessions"
    )
    ip_change_lockout: bool = Field(
        default=False, description="Lock out users on IP address change"
    )
    
    # File Upload Security
    max_upload_size: int = Field(
        default=10485760, ge=1024, description="Maximum file upload size in bytes"
    )  # 10MB default
    allowed_file_types: List[str] = Field(
        default=[".csv", ".xlsx", ".pdf", ".doc", ".docx", ".txt"],
        description="Allowed file extensions for upload"
    )
    scan_uploads: bool = Field(default=True, description="Enable virus scanning for uploads")
    quarantine_suspicious_files: bool = Field(
        default=True, description="Quarantine suspicious files"
    )
    
    # Rate Limiting
    rate_limiting_enabled: bool = Field(default=True, description="Enable rate limiting")
    rate_limit_requests: int = Field(default=100, ge=10, description="Requests per time window")
    rate_limit_window_minutes: int = Field(default=15, ge=1, description="Rate limit time window")
    rate_limit_burst: int = Field(default=20, ge=5, description="Burst allowance for rate limiting")
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173"],
        description="Allowed CORS origins"
    )
    cors_allow_credentials: bool = Field(default=True, description="Allow CORS credentials")
    cors_allow_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="Allowed CORS methods"
    )
    cors_allow_headers: List[str] = Field(
        default=["*"], description="Allowed CORS headers"
    )
    
    # HTTPS and Security Headers
    enforce_https: bool = Field(default=False, description="Enforce HTTPS connections")
    hsts_max_age: int = Field(default=31536000, description="HSTS max age in seconds")
    content_security_policy: Optional[str] = Field(
        default=None, description="Content Security Policy header value"
    )
    
    # Password Policy
    min_password_length: int = Field(default=8, ge=6, description="Minimum password length")
    require_uppercase: bool = Field(default=True, description="Require uppercase letters")
    require_lowercase: bool = Field(default=True, description="Require lowercase letters")
    require_numbers: bool = Field(default=True, description="Require numbers")
    require_special_chars: bool = Field(default=True, description="Require special characters")
    password_history_count: int = Field(default=5, ge=0, description="Password history count")
    
    # Account Security
    max_login_attempts: int = Field(default=5, ge=3, description="Maximum login attempts")
    lockout_duration_minutes: int = Field(default=15, ge=5, description="Account lockout duration")
    require_email_verification: bool = Field(default=True, description="Require email verification")
    two_factor_auth_enabled: bool = Field(default=False, description="Enable 2FA")
    
    @field_validator('jwt_secret_key')
    @classmethod
    def validate_jwt_secret(cls, v: str) -> str:
        """Validate JWT secret key strength."""
        if len(v) < 32:
            raise ValueError('JWT secret key must be at least 32 characters long')

        # Check for weak default secrets
        weak_patterns = [
            "your-secret-key",
            "development-only",
            "test-secret",
            "default-key",
            "changeme"
        ]

        v_lower = v.lower()
        for pattern in weak_patterns:
            if pattern in v_lower:
                logger.warning(f"JWT secret contains weak pattern: {pattern}")
                raise ValueError(f'JWT secret contains weak pattern. Use a strong, unique secret.')

        return v
    
    @field_validator('jwt_algorithm')
    @classmethod
    def validate_jwt_algorithm(cls, v: str) -> str:
        """Validate JWT algorithm."""
        allowed_algorithms = ['HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512']
        if v not in allowed_algorithms:
            raise ValueError(f'JWT algorithm must be one of: {allowed_algorithms}')
        return v
    
    @field_validator('allowed_file_types')
    @classmethod
    def validate_file_types(cls, v: List[str]) -> List[str]:
        """Validate file type extensions."""
        validated_types = []
        for file_type in v:
            if not file_type.startswith('.'):
                file_type = '.' + file_type
            validated_types.append(file_type.lower())
        return validated_types
    
    @computed_field
    @property
    def max_upload_size_mb(self) -> float:
        """Get maximum upload size in megabytes."""
        return self.max_upload_size / (1024 * 1024)
    
    @computed_field
    @property
    def password_policy_description(self) -> str:
        """Generate password policy description."""
        requirements = [f"at least {self.min_password_length} characters"]
        
        if self.require_uppercase:
            requirements.append("uppercase letters")
        if self.require_lowercase:
            requirements.append("lowercase letters")
        if self.require_numbers:
            requirements.append("numbers")
        if self.require_special_chars:
            requirements.append("special characters")
        
        return "Password must contain " + ", ".join(requirements)
    
    @classmethod
    def from_env(cls) -> "SecurityConfig":
        """Create security configuration from environment variables."""
        from ..security.jwt_security import get_secure_jwt_secret

        cors_origins = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:5173")
        allowed_file_types = os.getenv("ALLOWED_FILE_TYPES", ".csv,.xlsx,.pdf,.doc,.docx,.txt")

        # Get secure JWT secret
        jwt_secret = get_secure_jwt_secret()

        return cls(
            jwt_secret_key=jwt_secret,
            jwt_algorithm=os.getenv("JWT_ALGORITHM", "HS256"),
            access_token_expire_minutes=int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30")),
            refresh_token_expire_days=int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7")),
            max_refresh_count=int(os.getenv("MAX_REFRESH_COUNT", "30")),
            max_concurrent_sessions=int(os.getenv("MAX_CONCURRENT_SESSIONS", "5")),
            enforce_ip_validation=os.getenv("ENFORCE_IP_VALIDATION", "false").lower() == "true",
            ip_change_lockout=os.getenv("IP_CHANGE_LOCKOUT", "false").lower() == "true",
            max_upload_size=int(os.getenv("MAX_UPLOAD_SIZE", "10485760")),
            allowed_file_types=allowed_file_types.split(","),
            scan_uploads=os.getenv("SCAN_UPLOADS", "true").lower() == "true",
            quarantine_suspicious_files=os.getenv("QUARANTINE_SUSPICIOUS_FILES", "true").lower() == "true",
            rate_limiting_enabled=os.getenv("RATE_LIMITING_ENABLED", "true").lower() == "true",
            rate_limit_requests=int(os.getenv("RATE_LIMIT_REQUESTS", "100")),
            rate_limit_window_minutes=int(os.getenv("RATE_LIMIT_WINDOW_MINUTES", "15")),
            rate_limit_burst=int(os.getenv("RATE_LIMIT_BURST", "20")),
            cors_origins=cors_origins.split(","),
            cors_allow_credentials=os.getenv("CORS_ALLOW_CREDENTIALS", "true").lower() == "true",
            enforce_https=os.getenv("ENFORCE_HTTPS", "false").lower() == "true",
            hsts_max_age=int(os.getenv("HSTS_MAX_AGE", "31536000")),
            content_security_policy=os.getenv("CONTENT_SECURITY_POLICY"),
            min_password_length=int(os.getenv("MIN_PASSWORD_LENGTH", "8")),
            require_uppercase=os.getenv("REQUIRE_UPPERCASE", "true").lower() == "true",
            require_lowercase=os.getenv("REQUIRE_LOWERCASE", "true").lower() == "true",
            require_numbers=os.getenv("REQUIRE_NUMBERS", "true").lower() == "true",
            require_special_chars=os.getenv("REQUIRE_SPECIAL_CHARS", "true").lower() == "true",
            password_history_count=int(os.getenv("PASSWORD_HISTORY_COUNT", "5")),
            max_login_attempts=int(os.getenv("MAX_LOGIN_ATTEMPTS", "5")),
            lockout_duration_minutes=int(os.getenv("LOCKOUT_DURATION_MINUTES", "15")),
            require_email_verification=os.getenv("REQUIRE_EMAIL_VERIFICATION", "true").lower() == "true",
            two_factor_auth_enabled=os.getenv("TWO_FACTOR_AUTH_ENABLED", "false").lower() == "true"
        )
