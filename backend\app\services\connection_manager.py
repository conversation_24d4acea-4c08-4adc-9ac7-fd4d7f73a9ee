"""
Enhanced Database Connection Manager for Phase 2 Memory Leak Resolution.

This module provides comprehensive database connection management with:
- Connection timeout and retry logic
- Connection pool monitoring
- Connection leak detection
- Automatic cleanup and recovery
"""

import logging
import asyncio
import time
import weakref
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass
from sqlalchemy import create_engine, event, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import DisconnectionError, TimeoutError, OperationalError
import gc

logger = logging.getLogger(__name__)


@dataclass
class ConnectionInfo:
    """Information about a database connection."""
    connection_id: str
    created_at: datetime
    last_used: datetime
    query_count: int = 0
    is_active: bool = True
    stack_trace: Optional[str] = None


@dataclass
class ConnectionPoolStats:
    """Statistics about the connection pool."""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    checked_out_connections: int = 0
    overflow_connections: int = 0
    invalid_connections: int = 0
    pool_size: int = 0
    max_overflow: int = 0
    connection_leaks: int = 0
    avg_checkout_time: float = 0.0
    total_checkouts: int = 0
    failed_checkouts: int = 0


class EnhancedConnectionManager:
    """
    Enhanced database connection manager with comprehensive monitoring,
    leak detection, and automatic recovery capabilities.
    """
    
    def __init__(self, database_url: str, **engine_kwargs):
        """Initialize the enhanced connection manager."""
        self.database_url = database_url
        self.engine_kwargs = engine_kwargs
        self.logger = logging.getLogger(__name__)
        
        # Connection tracking
        self._active_connections: Dict[str, ConnectionInfo] = {}
        self._connection_weak_refs: Dict[str, weakref.ref] = {}
        self._checkout_times: Dict[str, float] = {}
        
        # Pool monitoring
        self._pool_stats = ConnectionPoolStats()
        self._last_stats_update = datetime.now()
        
        # Leak detection
        self._leak_detection_enabled = True
        self._max_connection_age = timedelta(hours=1)
        self._max_idle_time = timedelta(minutes=30)
        self._leak_threshold = 50  # Number of connections before considering it a leak
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self._is_shutting_down = False
        
        # Create enhanced engine
        self.engine = self._create_enhanced_engine()
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        self.logger.info("Enhanced connection manager initialized")
    
    def _create_enhanced_engine(self) -> Engine:
        """Create database engine with enhanced monitoring and error handling."""
        # Enhanced connection pool configuration
        pool_config = {
            "pool_size": 20,
            "max_overflow": 30,
            "pool_pre_ping": True,
            "pool_recycle": 3600,
            "pool_timeout": 30,  # Timeout for getting connection from pool
            "pool_reset_on_return": "commit",
            "pool_logging_name": "datagenius_enhanced_pool",
        }
        
        # Merge with provided engine kwargs
        final_kwargs = {**self.engine_kwargs, **pool_config}
        
        # Create engine with enhanced configuration
        engine = create_engine(self.database_url, **final_kwargs)
        
        # Register event listeners for monitoring
        self._register_event_listeners(engine)
        
        return engine
    
    def _register_event_listeners(self, engine: Engine):
        """Register SQLAlchemy event listeners for monitoring."""
        
        @event.listens_for(engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Handle new database connections."""
            connection_id = str(id(dbapi_connection))
            
            # Track connection
            self._active_connections[connection_id] = ConnectionInfo(
                connection_id=connection_id,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            # Create weak reference for leak detection
            def cleanup_callback(ref):
                if connection_id in self._connection_weak_refs:
                    del self._connection_weak_refs[connection_id]
                self.logger.debug(f"Connection {connection_id} weak reference cleaned up")
            
            self._connection_weak_refs[connection_id] = weakref.ref(
                dbapi_connection, cleanup_callback
            )
            
            self.logger.debug(f"New database connection created: {connection_id}")
        
        @event.listens_for(engine, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            """Handle connection checkout from pool."""
            connection_id = str(id(dbapi_connection))
            self._checkout_times[connection_id] = time.time()
            
            if connection_id in self._active_connections:
                self._active_connections[connection_id].last_used = datetime.now()
                self._active_connections[connection_id].is_active = True
            
            self._pool_stats.total_checkouts += 1
        
        @event.listens_for(engine, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            """Handle connection checkin to pool."""
            connection_id = str(id(dbapi_connection))
            
            # Calculate checkout time
            if connection_id in self._checkout_times:
                checkout_time = time.time() - self._checkout_times[connection_id]
                self._update_avg_checkout_time(checkout_time)
                del self._checkout_times[connection_id]
            
            if connection_id in self._active_connections:
                self._active_connections[connection_id].is_active = False
        
        @event.listens_for(engine, "close")
        def on_close(dbapi_connection, connection_record):
            """Handle connection close."""
            connection_id = str(id(dbapi_connection))
            
            # Clean up tracking
            if connection_id in self._active_connections:
                del self._active_connections[connection_id]
            if connection_id in self._connection_weak_refs:
                del self._connection_weak_refs[connection_id]
            if connection_id in self._checkout_times:
                del self._checkout_times[connection_id]
            
            self.logger.debug(f"Database connection closed: {connection_id}")
    
    def _update_avg_checkout_time(self, checkout_time: float):
        """Update average checkout time statistics."""
        current_avg = self._pool_stats.avg_checkout_time
        total_checkouts = self._pool_stats.total_checkouts
        
        if total_checkouts > 0:
            self._pool_stats.avg_checkout_time = (
                (current_avg * (total_checkouts - 1) + checkout_time) / total_checkouts
            )
        else:
            self._pool_stats.avg_checkout_time = checkout_time
    
    @asynccontextmanager
    async def get_session_with_retry(self, max_retries: int = 3, retry_delay: float = 1.0):
        """
        Get database session with automatic retry logic.
        
        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        """
        session = None
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                session = self.SessionLocal()
                yield session
                session.commit()
                return
                
            except (DisconnectionError, TimeoutError, OperationalError) as e:
                last_exception = e
                
                if session:
                    session.rollback()
                    session.close()
                    session = None
                
                if attempt < max_retries:
                    self.logger.warning(
                        f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}): {e}. "
                        f"Retrying in {retry_delay}s..."
                    )
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"All {max_retries + 1} database attempts failed: {e}")
                    self._pool_stats.failed_checkouts += 1
                    
            except Exception as e:
                if session:
                    session.rollback()
                    session.close()
                raise e
                
        # If we get here, all retries failed
        if last_exception:
            raise last_exception
    
    def get_session(self) -> Session:
        """Get a database session (traditional method)."""
        return self.SessionLocal()
    
    def get_pool_statistics(self) -> ConnectionPoolStats:
        """Get current connection pool statistics."""
        return self._pool_stats
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get detailed connection information."""
        current_time = datetime.now()
        
        connection_details = []
        for conn_id, conn_info in self._active_connections.items():
            age = current_time - conn_info.created_at
            idle_time = current_time - conn_info.last_used
            
            connection_details.append({
                'connection_id': conn_id,
                'age_seconds': age.total_seconds(),
                'idle_seconds': idle_time.total_seconds(),
                'query_count': conn_info.query_count,
                'is_active': conn_info.is_active,
                'created_at': conn_info.created_at.isoformat(),
                'last_used': conn_info.last_used.isoformat()
            })
        
        return {
            'total_connections': len(self._active_connections),
            'active_connections': len([c for c in self._active_connections.values() if c.is_active]),
            'connection_details': connection_details,
            'pool_stats': {
                'pool_size': self._pool_stats.pool_size,
                'checked_out': self._pool_stats.checked_out_connections,
                'overflow': self._pool_stats.overflow_connections,
                'invalid': self._pool_stats.invalid_connections,
                'avg_checkout_time': self._pool_stats.avg_checkout_time,
                'total_checkouts': self._pool_stats.total_checkouts,
                'failed_checkouts': self._pool_stats.failed_checkouts,
                'connection_leaks': self._pool_stats.connection_leaks
            }
        }


# Global connection manager instance (lazy initialization)
_connection_manager: Optional[EnhancedConnectionManager] = None


def get_connection_manager(database_url: Optional[str] = None, **kwargs) -> EnhancedConnectionManager:
    """Get the global connection manager instance (lazy initialization)."""
    global _connection_manager
    
    if _connection_manager is None and database_url:
        _connection_manager = EnhancedConnectionManager(database_url, **kwargs)
    
    return _connection_manager


def get_db_session():
    """Get database session using the global connection manager."""
    connection_manager = get_connection_manager()
    if connection_manager:
        return connection_manager.get_session()
    return None


@asynccontextmanager
async def get_db_session_with_retry(max_retries: int = 3, retry_delay: float = 1.0):
    """Get database session with retry logic using the global connection manager."""
    connection_manager = get_connection_manager()
    if connection_manager:
        async with connection_manager.get_session_with_retry(max_retries, retry_delay) as session:
            yield session
    else:
        yield None
