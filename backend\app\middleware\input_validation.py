"""
Comprehensive input validation middleware.

This module provides comprehensive input validation for all user inputs
including request validation, rate limiting, and security checks.
"""

import logging
import time
import json
import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from collections import defaultdict

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, field_validator
import bleach

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors."""
    
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message)
        self.field = field
        self.value = value
        self.timestamp = datetime.now()


class RateLimiter:
    """Rate limiter for validation endpoints."""
    
    def __init__(self):
        self.requests = defaultdict(list)
        self.max_requests = 100  # requests per window
        self.window_minutes = 15
        self.burst_limit = 20
    
    def is_allowed(self, client_ip: str) -> bool:
        """Check if request is allowed based on rate limits."""
        now = datetime.now()
        window_start = now - timedelta(minutes=self.window_minutes)
        
        # Clean old requests
        self.requests[client_ip] = [
            req_time for req_time in self.requests[client_ip]
            if req_time > window_start
        ]
        
        # Check rate limits
        recent_requests = len(self.requests[client_ip])
        
        # Check burst limit (last minute)
        burst_start = now - timedelta(minutes=1)
        burst_requests = len([
            req_time for req_time in self.requests[client_ip]
            if req_time > burst_start
        ])
        
        if burst_requests >= self.burst_limit:
            logger.warning(f"Burst rate limit exceeded for IP: {client_ip}")
            return False
        
        if recent_requests >= self.max_requests:
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return False
        
        # Record this request
        self.requests[client_ip].append(now)
        return True


class InputSanitizer:
    """Sanitizes user inputs to prevent injection attacks."""
    
    def __init__(self):
        self.allowed_tags = ['b', 'i', 'u', 'em', 'strong', 'p', 'br']
        self.allowed_attributes = {}
    
    def sanitize_html(self, text: str) -> str:
        """Sanitize HTML content."""
        if not isinstance(text, str):
            return str(text)
        
        return bleach.clean(
            text,
            tags=self.allowed_tags,
            attributes=self.allowed_attributes,
            strip=True
        )
    
    def sanitize_sql_input(self, text: str) -> str:
        """Sanitize input to prevent SQL injection."""
        if not isinstance(text, str):
            return str(text)
        
        # Remove dangerous SQL keywords and characters
        dangerous_patterns = [
            r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)',
            r'(--|/\*|\*/)',
            r'(\bUNION\b)',
            r'(\bOR\b\s+\d+\s*=\s*\d+)',
            r'(\bAND\b\s+\d+\s*=\s*\d+)',
            r'(\'|\";|;)',
        ]
        
        sanitized = text
        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        return sanitized.strip()
    
    def sanitize_script_input(self, text: str) -> str:
        """Sanitize input to prevent script injection."""
        if not isinstance(text, str):
            return str(text)
        
        # Remove script tags and dangerous JavaScript
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'eval\s*\(',
            r'setTimeout\s*\(',
            r'setInterval\s*\(',
        ]
        
        sanitized = text
        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        return sanitized


class InputValidator:
    """Comprehensive input validator."""
    
    def __init__(self):
        self.sanitizer = InputSanitizer()
        self.rate_limiter = RateLimiter()
        
        # Maximum sizes for different input types
        self.max_sizes = {
            'text': 10000,      # 10KB for text inputs
            'json': 100000,     # 100KB for JSON data
            'file_name': 255,   # Standard filename length
            'email': 254,       # RFC 5321 limit
            'url': 2048,        # Common URL length limit
        }
    
    def validate_text_input(self, text: str, field_name: str = "text", 
                           max_length: int = None, allow_html: bool = False) -> str:
        """Validate and sanitize text input."""
        if not isinstance(text, str):
            raise ValidationError(f"Field {field_name} must be a string", field_name, text)
        
        # Check length
        max_len = max_length or self.max_sizes['text']
        if len(text) > max_len:
            raise ValidationError(
                f"Field {field_name} exceeds maximum length of {max_len} characters",
                field_name, len(text)
            )
        
        # Sanitize based on type
        if allow_html:
            sanitized = self.sanitizer.sanitize_html(text)
        else:
            sanitized = self.sanitizer.sanitize_script_input(text)
            sanitized = self.sanitizer.sanitize_sql_input(sanitized)
        
        return sanitized
    
    def validate_email(self, email: str) -> str:
        """Validate email address."""
        if not isinstance(email, str):
            raise ValidationError("Email must be a string", "email", email)
        
        if len(email) > self.max_sizes['email']:
            raise ValidationError("Email address too long", "email", len(email))
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError("Invalid email format", "email", email)
        
        return email.lower().strip()
    
    def validate_url(self, url: str) -> str:
        """Validate URL."""
        if not isinstance(url, str):
            raise ValidationError("URL must be a string", "url", url)
        
        if len(url) > self.max_sizes['url']:
            raise ValidationError("URL too long", "url", len(url))
        
        # Basic URL validation
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        if not re.match(url_pattern, url):
            raise ValidationError("Invalid URL format", "url", url)
        
        return url.strip()
    
    def validate_json_data(self, data: Union[str, Dict, List]) -> Dict[str, Any]:
        """Validate JSON data."""
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError as e:
                raise ValidationError(f"Invalid JSON format: {str(e)}", "json_data", data)
        
        # Check size
        json_str = json.dumps(data)
        if len(json_str) > self.max_sizes['json']:
            raise ValidationError("JSON data too large", "json_data", len(json_str))
        
        return data
    
    def validate_file_name(self, filename: str) -> str:
        """Validate file name."""
        if not isinstance(filename, str):
            raise ValidationError("Filename must be a string", "filename", filename)
        
        if len(filename) > self.max_sizes['file_name']:
            raise ValidationError("Filename too long", "filename", len(filename))
        
        # Check for dangerous characters
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                raise ValidationError(f"Filename contains dangerous character: {char}", "filename", filename)
        
        return filename.strip()


# Global instances
input_validator = InputValidator()
rate_limiter = RateLimiter()


async def validate_request_middleware(request: Request, call_next):
    """Middleware for comprehensive request validation."""
    start_time = time.time()
    
    try:
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Rate limiting check
        if not rate_limiter.is_allowed(client_ip):
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Rate limit exceeded",
                    "message": "Too many requests. Please try again later.",
                    "retry_after": 900  # 15 minutes
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Log successful request
        processing_time = time.time() - start_time
        logger.info(f"Request processed: {request.method} {request.url.path} - {processing_time:.3f}s")
        
        return response
        
    except Exception as e:
        logger.error(f"Request validation error: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": "Request validation failed",
                "message": "Invalid request format or content"
            }
        )
