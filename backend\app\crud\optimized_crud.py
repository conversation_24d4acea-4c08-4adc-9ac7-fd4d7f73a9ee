"""
Optimized CRUD operations for Phase 3 performance improvements.

This module provides high-performance database operations using:
- Batch operations for bulk inserts/updates/deletes
- Query caching for frequently accessed data
- Optimized query patterns to avoid N+1 problems
- Connection pooling optimization
"""

import logging
from typing import Dict, Any, List, Optional, Type, Union
from datetime import datetime
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import and_, or_, func, text

from ..database import Base, get_db
from ..models.conversation import Conversation, Message
from ..models.user import User
from ..models.file import File
from ..models.task import Task
from ..models.business_profile import BusinessProfile
from ..models.persona import Persona, PersonaVersion
from ..models.purchase import Purchase
from ..performance.database_optimization import batch_manager, query_cache

logger = logging.getLogger(__name__)


class OptimizedCRUD:
    """
    Optimized CRUD operations with batch processing and caching.
    
    Provides high-performance database operations that replace
    individual CRUD functions with batch-optimized versions.
    """
    
    def __init__(self):
        self.batch_manager = batch_manager
        self.query_cache = query_cache
    
    # Conversation Operations
    def create_conversations_batch(self, db: Session, conversations_data: List[Dict[str, Any]]) -> List[Conversation]:
        """Create multiple conversations in a single batch operation."""
        return self.batch_manager.batch_insert(db, Conversation, conversations_data)
    
    def get_user_conversations_optimized(self, db: Session, user_id: int, 
                                       skip: int = 0, limit: int = 100) -> List[Conversation]:
        """Get user conversations with optimized query and caching."""
        cache_key = f"user_conversations:{user_id}:{skip}:{limit}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return [Conversation(**conv) for conv in cached_result]
        
        # Optimized query with eager loading
        conversations = db.query(Conversation).filter(
            Conversation.user_id == user_id,
            Conversation.is_archived == False
        ).options(
            selectinload(Conversation.messages)
        ).order_by(
            Conversation.updated_at.desc()
        ).offset(skip).limit(limit).all()
        
        # Cache the result
        conv_data = [conv.to_dict() for conv in conversations]
        self.query_cache.cache_result(cache_key, conv_data, ttl=300)
        
        return conversations
    
    def update_conversations_batch(self, db: Session, updates: List[Dict[str, Any]]) -> int:
        """Update multiple conversations in a single batch operation."""
        # Invalidate related cache entries
        for update in updates:
            if 'user_id' in update:
                self.query_cache.invalidate_cache_pattern(f"user_conversations:{update['user_id']}:*")
        
        return self.batch_manager.batch_update(db, Conversation, updates)
    
    # Message Operations
    def create_messages_batch(self, db: Session, messages_data: List[Dict[str, Any]]) -> List[Message]:
        """Create multiple messages in a single batch operation."""
        return self.batch_manager.batch_insert(db, Message, messages_data)
    
    def get_conversation_messages_optimized(self, db: Session, conversation_id: str,
                                          skip: int = 0, limit: int = 100) -> List[Message]:
        """Get conversation messages with optimized query and caching."""
        cache_key = f"conversation_messages:{conversation_id}:{skip}:{limit}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return [Message(**msg) for msg in cached_result]
        
        # Optimized query
        messages = db.query(Message).filter(
            Message.conversation_id == conversation_id
        ).order_by(
            Message.message_sequence.asc()
        ).offset(skip).limit(limit).all()
        
        # Cache the result
        msg_data = [msg.to_dict() for msg in messages]
        self.query_cache.cache_result(cache_key, msg_data, ttl=300)
        
        return messages
    
    def get_message_threads_optimized(self, db: Session, thread_ids: List[str]) -> Dict[str, List[Message]]:
        """Get multiple message threads in a single optimized query."""
        # Use a single query to get all messages for all threads
        messages = db.query(Message).filter(
            Message.thread_id.in_(thread_ids)
        ).order_by(
            Message.thread_id, Message.thread_sequence.asc()
        ).all()
        
        # Group messages by thread_id
        threads = {}
        for message in messages:
            if message.thread_id not in threads:
                threads[message.thread_id] = []
            threads[message.thread_id].append(message)
        
        return threads
    
    # User Operations
    def get_users_with_stats(self, db: Session, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """Get users with aggregated statistics in a single query."""
        cache_key = f"users_with_stats:{skip}:{limit}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Single query with subqueries for statistics
        query = db.query(
            User,
            func.count(Conversation.id).label('conversation_count'),
            func.count(File.id).label('file_count'),
            func.count(Task.id).label('task_count')
        ).outerjoin(Conversation).outerjoin(File).outerjoin(Task).group_by(User.id)
        
        results = query.offset(skip).limit(limit).all()
        
        # Format results
        users_data = []
        for user, conv_count, file_count, task_count in results:
            user_dict = user.to_dict()
            user_dict.update({
                'conversation_count': conv_count,
                'file_count': file_count,
                'task_count': task_count
            })
            users_data.append(user_dict)
        
        # Cache the result
        self.query_cache.cache_result(cache_key, users_data, ttl=600)
        
        return users_data
    
    # Business Profile Operations
    def get_business_profiles_with_personas(self, db: Session, user_id: int) -> List[Dict[str, Any]]:
        """Get business profiles with associated personas in optimized query."""
        cache_key = f"business_profiles_personas:{user_id}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Optimized query with eager loading
        profiles = db.query(BusinessProfile).filter(
            BusinessProfile.user_id == user_id
        ).options(
            selectinload(BusinessProfile.personas)
        ).all()
        
        # Format results
        profiles_data = []
        for profile in profiles:
            profile_dict = profile.to_dict()
            profile_dict['personas'] = [persona.to_dict() for persona in profile.personas]
            profiles_data.append(profile_dict)
        
        # Cache the result
        self.query_cache.cache_result(cache_key, profiles_data, ttl=600)
        
        return profiles_data
    
    # File Operations
    def create_files_batch(self, db: Session, files_data: List[Dict[str, Any]]) -> List[File]:
        """Create multiple files in a single batch operation."""
        return self.batch_manager.batch_insert(db, File, files_data)
    
    def get_user_files_with_tasks(self, db: Session, user_id: int) -> List[Dict[str, Any]]:
        """Get user files with associated tasks in optimized query."""
        cache_key = f"user_files_tasks:{user_id}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Optimized query with eager loading
        files = db.query(File).filter(
            File.user_id == user_id
        ).options(
            selectinload(File.tasks)
        ).all()
        
        # Format results
        files_data = []
        for file in files:
            file_dict = file.to_dict()
            file_dict['tasks'] = [task.to_dict() for task in file.tasks]
            files_data.append(file_dict)
        
        # Cache the result
        self.query_cache.cache_result(cache_key, files_data, ttl=300)
        
        return files_data
    
    # Task Operations
    def update_tasks_batch(self, db: Session, updates: List[Dict[str, Any]]) -> int:
        """Update multiple tasks in a single batch operation."""
        # Invalidate related cache entries
        for update in updates:
            if 'user_id' in update:
                self.query_cache.invalidate_cache_pattern(f"user_files_tasks:{update['user_id']}")
        
        return self.batch_manager.batch_update(db, Task, updates)
    
    def get_tasks_by_status_optimized(self, db: Session, status: str, 
                                    limit: int = 100) -> List[Task]:
        """Get tasks by status with optimized query and caching."""
        cache_key = f"tasks_by_status:{status}:{limit}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return [Task(**task) for task in cached_result]
        
        # Optimized query
        tasks = db.query(Task).filter(
            Task.status == status
        ).order_by(
            Task.created_at.desc()
        ).limit(limit).all()
        
        # Cache the result
        tasks_data = [task.to_dict() for task in tasks]
        self.query_cache.cache_result(cache_key, tasks_data, ttl=180)
        
        return tasks
    
    # Analytics and Reporting Operations
    def get_dashboard_analytics(self, db: Session, user_id: int, 
                              date_from: datetime, date_to: datetime) -> Dict[str, Any]:
        """Get comprehensive dashboard analytics in optimized queries."""
        cache_key = f"dashboard_analytics:{user_id}:{date_from.date()}:{date_to.date()}"
        
        # Try cache first
        cached_result = self.query_cache.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Execute multiple optimized queries
        analytics = {}
        
        # Conversation analytics
        conv_stats = db.query(
            func.count(Conversation.id).label('total_conversations'),
            func.count(func.distinct(Conversation.persona_id)).label('unique_personas'),
            func.avg(func.extract('epoch', Conversation.updated_at - Conversation.created_at)).label('avg_duration')
        ).filter(
            Conversation.user_id == user_id,
            Conversation.created_at.between(date_from, date_to)
        ).first()
        
        analytics['conversations'] = {
            'total': conv_stats.total_conversations or 0,
            'unique_personas': conv_stats.unique_personas or 0,
            'avg_duration_seconds': conv_stats.avg_duration or 0
        }
        
        # Message analytics
        msg_stats = db.query(
            func.count(Message.id).label('total_messages'),
            func.avg(func.length(Message.content)).label('avg_message_length')
        ).join(Conversation).filter(
            Conversation.user_id == user_id,
            Message.created_at.between(date_from, date_to)
        ).first()
        
        analytics['messages'] = {
            'total': msg_stats.total_messages or 0,
            'avg_length': msg_stats.avg_message_length or 0
        }
        
        # File analytics
        file_stats = db.query(
            func.count(File.id).label('total_files'),
            func.sum(File.file_size).label('total_size'),
            func.avg(File.num_rows).label('avg_rows')
        ).filter(
            File.user_id == user_id,
            File.created_at.between(date_from, date_to)
        ).first()
        
        analytics['files'] = {
            'total': file_stats.total_files or 0,
            'total_size_bytes': file_stats.total_size or 0,
            'avg_rows': file_stats.avg_rows or 0
        }
        
        # Cache the result
        self.query_cache.cache_result(cache_key, analytics, ttl=900)  # 15 minutes
        
        return analytics
    
    # Cache Management
    def invalidate_user_cache(self, user_id: int):
        """Invalidate all cache entries for a specific user."""
        patterns = [
            f"user_conversations:{user_id}:*",
            f"business_profiles_personas:{user_id}",
            f"user_files_tasks:{user_id}",
            f"dashboard_analytics:{user_id}:*"
        ]
        
        for pattern in patterns:
            self.query_cache.invalidate_cache_pattern(pattern)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics."""
        return {
            'batch_operations': self.batch_manager.get_performance_metrics(),
            'query_cache': self.query_cache.get_cache_stats()
        }


# Global optimized CRUD instance
optimized_crud = OptimizedCRUD()
