# Pre-commit configuration for security linting and code quality
# Run with: pre-commit run --all-files

repos:
  # Security scanning with bandit
  - repo: https://github.com/PyCQA/bandit
    rev: '1.7.5'
    hooks:
      - id: bandit
        args: ['-r', 'backend/', '-f', 'json', '-o', 'bandit-report.json']
        exclude: ^(tests/|backend/tests/)
        
  # Secret detection with detect-secrets
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: package.lock.json
        
  # Code formatting with black
  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black
        language_version: python3
        
  # Import sorting with isort
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
        
  # Linting with flake8
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings, flake8-security]
        args: ['--max-line-length=88', '--extend-ignore=E203,W503']
        
  # Type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.6.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports]
        
  # Security linting for Python
  - repo: https://github.com/PyCQA/safety
    rev: 2.3.5
    hooks:
      - id: safety
        args: [--json, --output, safety-report.json]
        
  # YAML linting
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.32.0
    hooks:
      - id: yamllint
        args: [-d, relaxed]
        
  # JSON linting
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-json
      - id: check-yaml
      - id: check-toml
      - id: check-merge-conflict
      - id: check-added-large-files
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: mixed-line-ending
        
  # SQL linting
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.2
    hooks:
      - id: sqlfluff-lint
        additional_dependencies: ['sqlfluff-templater-dbt']
        
  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        
  # Custom security checks
  - repo: local
    hooks:
      - id: check-hardcoded-secrets
        name: Check for hardcoded secrets
        entry: python scripts/security_checks.py
        language: python
        files: \.(py|yaml|yml|json|env)$
        
      - id: validate-sql-queries
        name: Validate SQL queries for injection risks
        entry: python scripts/sql_security_check.py
        language: python
        files: \.py$
        
      - id: check-jwt-security
        name: Check JWT security configuration
        entry: python scripts/jwt_security_check.py
        language: python
        files: \.(py|yaml|yml)$
