"""
Authentication API endpoints for the Datagenius backend.

This module provides API endpoints for user authentication.
"""

import logging
import secrets
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from urllib.parse import urlencode

from fastapi import APIRouter, Depends, HTTPException, status, Response, Cookie, Request
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
import httpx
from sqlalchemy.orm import Session

from ..models.auth import (
    User, UserCreate, UserUpdate, Token, LoginRequest,
    RefreshTokenRequest, PasswordChangeRequest, GoogleAuthRequest,
    OAuthRequest, OAuthUserInfo
)
from pydantic import BaseModel
from ..database import get_db, get_user_by_email, get_user_by_id, get_user_by_oauth
from ..auth import (
    authenticate_user, create_access_token, create_refresh_token,
    get_password_hash, get_current_active_user, blacklist_token,
    validate_refresh_token, invalidate_refresh_token
)
from ..redis_client import get_token_refresh_count, increment_token_refresh_count, get_token_metadata, set_with_expiry, get_value
from ..utils.ip_utils import hash_ip_address, anonymize_ip_for_logging
from .. import config

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/auth", tags=["Authentication"])


def generate_error_correlation_id() -> str:
    """Generate a unique correlation ID for error tracking."""
    return f"auth_{uuid.uuid4().hex[:8]}"


def sanitize_error_for_user(error: Exception, correlation_id: str) -> str:
    """Sanitize error messages for user consumption while logging full details."""
    # Log the full error details with correlation ID for debugging
    logger.error(f"Authentication error [{correlation_id}]: {str(error)}", exc_info=True)

    # Return generic message to user
    return f"An authentication error occurred. Please try again or contact support with reference ID: {correlation_id}"


class IndustrySelectionRequest(BaseModel):
    """Request model for industry selection."""
    industry: str
    completed: bool = True


@router.post("/register", response_model=User)
async def register(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new user.

    Args:
        user_data: User registration data
        db: Database session

    Returns:
        User information
    """
    # Add detailed logging
    logger.info(f"Register endpoint called with data: {user_data}")
    logger.info(f"Registering new user with email {user_data.email}")

    # Check if user already exists
    existing_user = get_user_by_email(db, user_data.email)
    if existing_user:
        logger.warning(f"User with email {user_data.email} already exists")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new user
    hashed_password = get_password_hash(user_data.password)

    # Create user in database
    try:
        from ..database import User as DBUser
        db_user = DBUser(
            email=user_data.email,
            username=user_data.username,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            hashed_password=hashed_password,
            is_active=True,
            is_verified=False,
            is_superuser=False
        )

        logger.info(f"Created user object: {db_user}")
        db.add(db_user)
        logger.info("Added user to database session")
        db.commit()
        logger.info("Committed user to database")
        db.refresh(db_user)
        logger.info(f"User {db_user.id} created successfully")
    except Exception as e:
        correlation_id = generate_error_correlation_id()
        sanitized_message = sanitize_error_for_user(e, correlation_id)
        raise HTTPException(status_code=500, detail=sanitized_message)

    logger.info(f"User {db_user.id} registered successfully")

    return db_user


@router.post("/token", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Login with username and password.

    Args:
        form_data: OAuth2 password request form
        db: Database session

    Returns:
        JWT token
    """
    logger.info(f"Login attempt for user {form_data.username}")

    try:
        # Authenticate user
        logger.debug(f"Calling authenticate_user for {form_data.username}")
        user, error = await authenticate_user(db, form_data.username, form_data.password)
        logger.debug(f"authenticate_user returned: user={user is not None}, error={error}")

        if error:
            logger.warning(f"Login failed for user {form_data.username}: {error}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except Exception as e:
        correlation_id = generate_error_correlation_id()
        sanitized_message = sanitize_error_for_user(e, correlation_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=sanitized_message,
        )

    # Create access token
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )

    # Create refresh token
    refresh_token = create_refresh_token(user.id)

    logger.info(f"User {user.id} logged in successfully")

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/login", response_model=Token)
async def login_with_email(
    login_data: LoginRequest,
    db: Session = Depends(get_db),
    request: Request = None
):
    """
    Login with email and password.

    Args:
        login_data: Login request data
        db: Database session

    Returns:
        JWT token
    """
    logger.info(f"Login attempt for user {login_data.email}")

    try:
        # Authenticate user
        logger.debug(f"Calling authenticate_user for {login_data.email}")
        user, error = await authenticate_user(db, login_data.email, login_data.password)
        logger.debug(f"authenticate_user returned: user={user is not None}, error={error}")

        if error:
            logger.warning(f"Login failed for user {login_data.email}: {error}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except Exception as e:
        correlation_id = generate_error_correlation_id()
        sanitized_message = sanitize_error_for_user(e, correlation_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=sanitized_message,
        )

    # Create access token
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )

    # Create refresh token with device fingerprint if provided
    device_fingerprint = None
    device_info = None

    if hasattr(login_data, 'device_fingerprint'):
        device_fingerprint = login_data.device_fingerprint
        logger.info(f"Device fingerprint provided for user {user.id}: {device_fingerprint}")

    if hasattr(login_data, 'device_info'):
        device_info = login_data.device_info.model_dump() if login_data.device_info else None

        # Add hashed IP address to device info
        if device_info and request:
            client_ip = request.client.host if request.client else None
            if client_ip:
                # Hash the IP address before storing
                hashed_ip = hash_ip_address(client_ip, salt=config.JWT_SECRET_KEY)
                device_info['ip_address'] = hashed_ip
                # Log anonymized version of the IP
                anonymized_ip = anonymize_ip_for_logging(client_ip)
                logger.info(f"Hashed IP address added to device info: {anonymized_ip}")

        logger.info(f"Device info provided for user {user.id}")

    refresh_token = create_refresh_token(user.id, metadata=device_info, device_fingerprint=device_fingerprint)

    logger.info(f"User {user.id} logged in successfully")

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db),
    request: Request = None
):
    """
    Refresh access token.

    Args:
        refresh_data: Refresh token request data
        db: Database session

    Returns:
        New JWT token
    """
    logger.info("Token refresh attempt")

    # Validate refresh token with device fingerprint if provided
    device_fingerprint = None
    if hasattr(refresh_data, 'device_fingerprint'):
        device_fingerprint = refresh_data.device_fingerprint
        logger.info(f"Device fingerprint provided for token refresh: {device_fingerprint}")

    # First try with device fingerprint validation
    user_id = validate_refresh_token(refresh_data.refresh_token, device_fingerprint)

    # If validation fails with device fingerprint, try without it (more lenient for development)
    if not user_id and device_fingerprint:
        logger.warning("Device fingerprint validation failed, trying without fingerprint validation")
        user_id = validate_refresh_token(refresh_data.refresh_token, None)

        if user_id:
            logger.info("Token validated without device fingerprint - proceeding with refresh")

    if not user_id:
        logger.warning("Invalid refresh token - all validation attempts failed")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check refresh count
    refresh_count = get_token_refresh_count(refresh_data.refresh_token)
    if refresh_count >= config.MAX_REFRESH_COUNT:
        logger.warning(f"Maximum refresh count reached for token: {refresh_count}")
        invalidate_refresh_token(refresh_data.refresh_token)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Maximum token refreshes reached. Please log in again.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Validate IP address if available
    if request and hasattr(refresh_data, 'device_info') and refresh_data.device_info:
        client_ip = request.client.host if request.client else None
        if client_ip:
            # Get stored metadata
            stored_metadata = get_token_metadata(refresh_data.refresh_token)
            if stored_metadata and 'ip_address' in stored_metadata:
                # Hash current IP for comparison
                current_ip_hash = hash_ip_address(client_ip, salt=config.JWT_SECRET_KEY)
                stored_ip_hash = stored_metadata['ip_address']

                # Check if IP hashes don't match
                if current_ip_hash != stored_ip_hash:
                    anonymized_ip = anonymize_ip_for_logging(client_ip)
                    logger.warning(f"IP address changed during token refresh for user {user_id}. New IP: {anonymized_ip}")

                    # If IP validation is enforced, reject the token refresh
                    if config.ENFORCE_IP_VALIDATION:
                        logger.error(f"IP validation failed for user {user_id}. Token refresh rejected.")

                        # If IP change lockout is enabled, invalidate the token
                        if config.IP_CHANGE_LOCKOUT:
                            invalidate_refresh_token(refresh_data.refresh_token)

                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="Security validation failed. Please log in again.",
                            headers={"WWW-Authenticate": "Bearer"},
                        )

    # Increment refresh count
    expires_delta = timedelta(days=config.REFRESH_TOKEN_EXPIRE_DAYS).total_seconds()
    new_refresh_count = increment_token_refresh_count(refresh_data.refresh_token, int(expires_delta))
    logger.info(f"Incremented refresh count to {new_refresh_count} for token")

    # Get user
    user = get_user_by_id(db, user_id)
    if not user:
        logger.warning(f"User {user_id} not found")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        logger.warning(f"User {user_id} is inactive")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create new access token
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)},
        expires_delta=access_token_expires
    )

    # Create new refresh token with the same device fingerprint and incremented refresh count
    device_info = None
    if hasattr(refresh_data, 'device_info'):
        device_info = refresh_data.device_info.model_dump() if refresh_data.device_info else None

        # Add hashed IP address to device info
        if device_info and request:
            client_ip = request.client.host if request.client else None
            if client_ip:
                # Hash the IP address before storing
                hashed_ip = hash_ip_address(client_ip, salt=config.JWT_SECRET_KEY)
                device_info['ip_address'] = hashed_ip
                # Log anonymized version of the IP
                anonymized_ip = anonymize_ip_for_logging(client_ip)
                logger.info(f"Hashed IP address added to device info: {anonymized_ip}")

    new_refresh_token = create_refresh_token(
        user.id,
        metadata=device_info,
        device_fingerprint=device_fingerprint,
        refresh_count=new_refresh_count
    )

    # Invalidate old refresh token
    invalidate_refresh_token(refresh_data.refresh_token)

    logger.info(f"Token refreshed for user {user.id}")

    return {
        "access_token": access_token,
        "refresh_token": new_refresh_token,
        "token_type": "bearer",
        "expires_in": config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/logout")
async def logout(
    response: Response,
    current_user: User = Depends(get_current_active_user),
    refresh_token: Optional[str] = Cookie(None),
    access_token: Optional[str] = Cookie(None)
):
    """
    Logout user.

    Args:
        response: FastAPI response
        current_user: Current authenticated user
        refresh_token: Refresh token cookie
        access_token: Access token cookie

    Returns:
        Success message
    """
    logger.info(f"Logout for user {current_user.id}")

    # Blacklist access token if provided
    if access_token:
        blacklist_token(access_token)

    # Invalidate refresh token if provided
    if refresh_token:
        invalidate_refresh_token(refresh_token)

    # Clear cookies
    response.delete_cookie("access_token")
    response.delete_cookie("refresh_token")

    logger.info(f"User {current_user.id} logged out successfully")

    return {"message": "Logged out successfully"}


@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get current user information.

    Args:
        current_user: Current authenticated user

    Returns:
        User information
    """
    logger.info(f"Getting user info for user {current_user.id}")
    return current_user


@router.put("/me", response_model=User)
async def update_current_user_info(
    user_data: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update current user information.

    Args:
        user_data: User update data
        db: Database session
        current_user: Current authenticated user

    Returns:
        Updated user information
    """
    logger.info(f"Updating user info for user {current_user.id}")

    # Update user in database
    db_user = get_user_by_id(db, current_user.id)

    if user_data.email is not None:
        # Check if email is already taken
        existing_user = get_user_by_email(db, user_data.email)
        if existing_user and existing_user.id != current_user.id:
            logger.warning(f"Email {user_data.email} already taken")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        db_user.email = user_data.email

    if user_data.username is not None:
        db_user.username = user_data.username

    if user_data.first_name is not None:
        db_user.first_name = user_data.first_name

    if user_data.last_name is not None:
        db_user.last_name = user_data.last_name

    db_user.updated_at = datetime.now()

    db.commit()
    db.refresh(db_user)

    logger.info(f"User {db_user.id} updated successfully")

    return db_user


@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Change user password.

    Args:
        password_data: Password change request data
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"Changing password for user {current_user.id}")

    # Get user from database
    db_user = get_user_by_id(db, current_user.id)

    # Verify current password
    try:
        logger.debug(f"Verifying current password for user {current_user.id}")
        _, error = await authenticate_user(db, db_user.email, password_data.current_password)
        logger.debug(f"Password verification result: error={error}")

        if error:
            logger.warning(f"Invalid current password for user {current_user.id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid current password"
            )
    except Exception as e:
        correlation_id = generate_error_correlation_id()
        sanitized_message = sanitize_error_for_user(e, correlation_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=sanitized_message
        )

    # Update password
    db_user.hashed_password = get_password_hash(password_data.new_password)
    db_user.updated_at = datetime.now()

    db.commit()

    logger.info(f"Password changed for user {current_user.id}")

    return {"message": "Password changed successfully"}


@router.get("/google/login")
async def google_login(request: Request):
    """
    Initiate Google OAuth login flow.

    Redirects the user to the Google OAuth consent screen.
    """
    logger.info("Initiating Google OAuth login flow")

    # Generate a random state parameter for CSRF protection
    state = secrets.token_urlsafe(32)

    # Store state in Redis for validation (expires in 10 minutes)
    success = set_with_expiry(f"oauth_state:{state}", "1", 600)
    if success:
        logger.info(f"Generated and stored OAuth state parameter: {state[:10]}...")
    else:
        logger.warning("Failed to store OAuth state parameter, proceeding without state validation")

    # Build the authorization URL
    params = {
        "client_id": config.GOOGLE_CLIENT_ID,
        "redirect_uri": config.GOOGLE_REDIRECT_URI,
        "response_type": "code",
        "scope": "email profile",
        "state": state,
        "access_type": "offline",  # For refresh token
        "prompt": "consent"  # Force consent screen to always appear
    }

    auth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
    logger.info(f"Redirecting to Google OAuth URL: {auth_url[:100]}...")

    # Redirect to Google's OAuth page
    return RedirectResponse(url=auth_url)


@router.get("/google/callback")
async def google_callback_get(
    code: str,
    state: Optional[str] = None
):
    """
    Handle Google OAuth callback via GET request.

    This endpoint is called by Google after the user authorizes the application.
    It redirects to the frontend callback URL with the code and state parameters.
    """
    logger.info("Received Google OAuth callback via GET")

    # Redirect to the frontend callback URL
    frontend_callback_url = f"{config.FRONTEND_URL}/auth/google/callback?code={code}"
    if state:
        frontend_callback_url += f"&state={state}"

    logger.info(f"Redirecting to frontend callback URL: {frontend_callback_url}")
    return RedirectResponse(url=frontend_callback_url)


@router.post("/google/callback", response_model=Token)
async def google_callback(
    request: GoogleAuthRequest,
    db: Session = Depends(get_db)
):
    """
    Handle Google OAuth callback.

    Exchanges the authorization code for tokens and creates or updates the user.
    """
    logger.info("Processing Google OAuth callback")

    # Validate state parameter (CSRF protection) - making this optional for now
    if request.state:
        try:
            state_key = f"oauth_state:{request.state}"
            stored_state = get_value(state_key)

            if stored_state:
                # Delete the state from Redis after use
                from ..redis_client import delete_key
                delete_key(state_key)
                logger.info(f"Validated and cleared OAuth state: {request.state[:10]}...")
            else:
                # Just log a warning but continue with the flow
                logger.warning(f"State parameter not found in Redis: {request.state[:10]}...")
        except Exception as e:
            # Log the error but continue with the flow
            logger.error(f"Error validating state parameter: {str(e)}")

    try:
        # Exchange authorization code for tokens
        token_url = "https://oauth2.googleapis.com/token"
        data = {
            "client_id": config.GOOGLE_CLIENT_ID,
            "client_secret": config.GOOGLE_CLIENT_SECRET,
            "code": request.code,
            "redirect_uri": config.GOOGLE_REDIRECT_URI,
            "grant_type": "authorization_code"
        }

        logger.debug(f"Token exchange request data: {data}")

        async with httpx.AsyncClient() as client:
            response = await client.post(token_url, data=data)
            token_data = response.json()

            if response.status_code != 200:
                error_message = "Failed to authenticate with Google"
                if "error" in token_data:
                    # Check for specific error types
                    if token_data.get("error") == "invalid_grant":
                        error_message = "Authorization code has expired or already been used"
                    else:
                        error_message = f"Google authentication error: {token_data.get('error')}"

                logger.error(f"Google OAuth token exchange failed: {token_data}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=error_message
                )

            logger.info("Successfully exchanged code for Google tokens")

            # Get user info from Google
            access_token = token_data["access_token"]
            refresh_token = token_data.get("refresh_token")  # May not be present if user has already granted access

            userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"
            headers = {"Authorization": f"Bearer {access_token}"}

            userinfo_response = await client.get(userinfo_url, headers=headers)
            user_info = userinfo_response.json()

            if userinfo_response.status_code != 200:
                logger.error(f"Google OAuth userinfo failed: {user_info}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Failed to get user info from Google"
                )

            logger.info(f"Retrieved user info from Google: {user_info['email']}")

            # Check if user exists
            google_id = user_info["id"]
            email = user_info["email"]

            # Try to find user by OAuth ID first
            user = get_user_by_oauth(db, "google", google_id)

            if not user:
                # Try to find user by email
                user = get_user_by_email(db, email)

                if user:
                    # Update existing user with OAuth info
                    logger.info(f"Linking existing user {user.id} with Google account {google_id}")
                    user.oauth_provider = "google"
                    user.oauth_id = google_id
                    db.commit()
                else:
                    # Create new user
                    logger.info(f"Creating new user from Google account: {email}")
                    from ..database import User as DBUser
                    user = DBUser(
                        email=email,
                        username=email.split("@")[0],  # Use part of email as username
                        first_name=user_info.get("given_name"),
                        last_name=user_info.get("family_name"),
                        oauth_provider="google",
                        oauth_id=google_id,
                        is_active=True,
                        is_verified=True  # Auto-verify OAuth users
                    )
                    db.add(user)
                    db.commit()
                    db.refresh(user)
                    logger.info(f"Created new user {user.id} from Google account")

            # Create access token with longer expiration for OAuth users
            access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES * 2)  # Double the expiration time for OAuth users
            access_token = create_access_token(
                data={"sub": str(user.id)},
                expires_delta=access_token_expires
            )

            # Create refresh token
            refresh_token = create_refresh_token(user.id)

            logger.info(f"User {user.id} authenticated via Google OAuth")

            # Return tokens
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": config.ACCESS_TOKEN_EXPIRE_MINUTES * 2 * 60  # Double the expiration time for OAuth users
            }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        correlation_id = generate_error_correlation_id()
        sanitized_message = sanitize_error_for_user(e, correlation_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=sanitized_message
        )


@router.post("/industry-selection", response_model=User)
async def update_industry_selection(
    industry_data: IndustrySelectionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update user's industry selection.

    Args:
        industry_data: Industry selection data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Updated user information
    """
    try:
        # Get the user from database
        db_user = get_user_by_id(db, current_user.id)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Update industry selection
        db_user.selected_industry = industry_data.industry
        db_user.industry_selection_completed = industry_data.completed
        db_user.updated_at = datetime.now(datetime.UTC)

        db.commit()
        db.refresh(db_user)

        logger.info(f"Updated industry selection for user {current_user.id}: {industry_data.industry}")

        return db_user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating industry selection for user {current_user.id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update industry selection"
        )
