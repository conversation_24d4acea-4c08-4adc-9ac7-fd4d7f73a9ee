"""
Purchase API endpoints for the Datagenius backend.

This module provides API endpoints for purchase functionality.
"""

import logging
import asyncio
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..models.purchase import (
    PurchaseResponse, PurchaseListResponse, CreatePurchaseRequest,
    PurchasedItemResponse
)
from ..models.auth import User
from ..database import (
    get_db, create_purchase, add_purchased_item, get_purchase,
    get_user_purchases, update_purchase_status, get_cart_items, clear_cart
)
from ..auth import get_current_active_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/purchases", tags=["Purchases"])


@router.get("", response_model=PurchaseListResponse)
async def list_purchases(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List purchases.

    Args:
        skip: Number of purchases to skip
        limit: Maximum number of purchases to return
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of purchases
    """
    logger.info(f"User {current_user.id} listing purchases")

    purchases = get_user_purchases(db, current_user.id, skip, limit)
    purchase_responses = [PurchaseResponse.model_validate(purchase, from_attributes=True) for purchase in purchases]
    return {"purchases": purchase_responses}


@router.get("/{purchase_id}", response_model=PurchaseResponse)
async def get_purchase_details(
    purchase_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get purchase details.

    Args:
        purchase_id: ID of the purchase
        db: Database session
        current_user: Current authenticated user

    Returns:
        Purchase details
    """
    logger.info(f"User {current_user.id} getting purchase {purchase_id}")

    purchase = get_purchase(db, purchase_id)
    if not purchase:
        raise HTTPException(status_code=404, detail="Purchase not found")

    # Check if the purchase belongs to the user
    if purchase.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to view this purchase")

    return PurchaseResponse.model_validate(purchase, from_attributes=True)


@router.post("/create", response_model=PurchaseResponse)
async def create_purchase_from_cart(
    request: CreatePurchaseRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a purchase from the cart.

    Args:
        request: Create purchase request
        db: Database session
        current_user: Current authenticated user

    Returns:
        Created purchase
    """
    logger.info(f"User {current_user.id} creating purchase")

    # Get cart items
    cart_items = get_cart_items(db, current_user.id)
    if not cart_items:
        raise HTTPException(status_code=400, detail="Cart is empty")

    # Calculate total amount (in a real app, you would get prices from a database)
    # For now, we'll use a fixed price of $10 per persona
    price_per_persona = 10.0
    total_amount = sum(item.quantity * price_per_persona for item in cart_items)

    # Create purchase
    purchase = create_purchase(
        db,
        current_user.id,
        request.payment_method,
        total_amount
    )

    # Add purchased items
    for item in cart_items:
        add_purchased_item(
            db,
            purchase.id,
            item.persona_id,
            item.quantity,
            price_per_persona
        )

    # Update purchase status to completed
    purchase = update_purchase_status(db, purchase.id, "completed")
    logger.info(f"Updated purchase {purchase.id} status to completed")

    # Clear cart
    clear_cart(db, current_user.id)

    # Refresh user permissions to include the newly purchased personas
    try:
        from agents.tools.mcp.security.access_control import refresh_user_permissions
        refresh_user_permissions(str(current_user.id))
        logger.info(f"Refreshed permissions for user {current_user.id} after bulk purchase")
    except Exception as e:
        logger.warning(f"Failed to refresh permissions for user {current_user.id}: {e}")

    # Process purchase events for agent registration
    try:
        from agents.langgraph.core.purchase_to_agent_flow import (
            get_purchase_to_agent_flow_manager,
            PurchaseEvent
        )

        purchase_to_agent_flow_manager = get_purchase_to_agent_flow_manager()

        # Create purchase events for each item
        for item in cart_items:
            purchase_event = PurchaseEvent(
                purchase_id=purchase.id,
                user_id=str(current_user.id),
                persona_id=item.persona_id,
                purchase_timestamp=purchase.created_at,
                payment_status=purchase.payment_status,
                metadata={
                    "quantity": item.quantity,
                    "price": price_per_persona,
                    "payment_method": request.payment_method
                }
            )

            # Process purchase event asynchronously
            asyncio.create_task(
                purchase_to_agent_flow_manager.process_purchase_event(purchase_event)
            )

        logger.info(f"Initiated agent registration for {len(cart_items)} purchased personas")

    except ImportError:
        logger.warning("Purchase-to-agent flow manager not available")
    except Exception as e:
        logger.error(f"Error processing purchase events: {e}")

    # Get the purchase with items
    purchase = get_purchase(db, purchase.id)

    return PurchaseResponse.model_validate(purchase, from_attributes=True)


@router.put("/{purchase_id}/status", response_model=PurchaseResponse)
async def update_purchase_payment_status(
    purchase_id: str,
    status: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update purchase payment status.

    Args:
        purchase_id: ID of the purchase
        status: New payment status
        db: Database session
        current_user: Current authenticated user

    Returns:
        Updated purchase
    """
    logger.info(f"User {current_user.id} updating purchase {purchase_id} status to {status}")

    # Check if the status is valid
    valid_statuses = ["pending", "completed", "failed"]
    if status not in valid_statuses:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
        )

    # Get purchase
    purchase = get_purchase(db, purchase_id)
    if not purchase:
        raise HTTPException(status_code=404, detail="Purchase not found")

    # Check if the purchase belongs to the user
    if purchase.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this purchase")

    # Update status
    updated_purchase = update_purchase_status(db, purchase_id, status)

    return PurchaseResponse.model_validate(updated_purchase, from_attributes=True)
