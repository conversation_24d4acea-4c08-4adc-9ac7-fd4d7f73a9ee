"""
Enhanced Personas API endpoints for the enhanced marketplace architecture.

This module provides API endpoints for enhanced personas functionality including
marketplace personas retrieval, workflow creation, and adaptive workflow management.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..database import get_db, PurchasedItem, Purchase
from ..models.auth import User
from ..auth import get_current_active_user
from ..services.enhanced_marketplace_service import (
    persona_config_service,
    agent_plugin_service,
    workflow_adaptation_service
)

# Import LangGraph components
try:
    from agents.langgraph.core.marketplace_agent_factory import (
        MarketplaceAgentFactory,
        UserContext,
        AgentDefinition,
        get_marketplace_agent_factory
    )
    from agents.langgraph.core.persona_workflow_builder import PersonaWorkflowBuilder
    from agents.langgraph.core.adaptive_workflow_manager import AdaptiveWorkflowManager
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/enhanced-personas", tags=["Enhanced Personas"])

# Initialize LangGraph components (lazy initialization)
def get_marketplace_factory():
    if LANGGRAPH_AVAILABLE:
        return get_marketplace_agent_factory()
    return None
persona_workflow_builder = PersonaWorkflowBuilder() if LANGGRAPH_AVAILABLE else None
adaptive_workflow_manager = AdaptiveWorkflowManager() if LANGGRAPH_AVAILABLE else None


@router.get("/marketplace/{user_id}")
async def get_user_marketplace_personas(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's purchased personas from marketplace with agent instantiation capability."""

    if not LANGGRAPH_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Enhanced marketplace functionality not available"
        )

    try:
        logger.info(f"Getting marketplace personas for user {user_id}")

        # Verify user access
        if str(current_user.id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Get purchased personas from database
        purchased_personas = db.query(PurchasedItem.persona_id).join(
            Purchase, PurchasedItem.purchase_id == Purchase.id
        ).filter(
            Purchase.user_id == int(user_id),
            Purchase.payment_status == "completed"
        ).distinct().all()

        persona_ids = [persona[0] for persona in purchased_personas]

        # Get agent definitions from marketplace factory
        agent_definitions = []
        marketplace_factory = get_marketplace_factory()
        if marketplace_factory:
            for persona_id in persona_ids:
                try:
                    # Get persona configuration
                    try:
                        from agents.langgraph.core.persona_configuration_manager import persona_config_manager
                        config = await persona_config_manager.get_persona_configuration(persona_id)
                    except ImportError:
                        # Fallback to basic configuration
                        config = {
                            "persona_id": persona_id,
                            "name": persona_id.replace("_", " ").title(),
                            "description": f"AI assistant specialized in {persona_id}",
                            "capabilities": [],
                            "skills": []
                        }

                    if config:
                        # Create agent definition
                        definition = {
                            "persona_id": persona_id,
                            "name": config.get("name", persona_id.replace("_", " ").title()),
                            "description": config.get("description", f"AI assistant specialized in {persona_id}"),
                            "industry": config.get("industry_specialization", "general"),
                            "capabilities": config.get("capabilities", []),
                            "skills": config.get("skills", []),
                            "is_purchased": True,
                            "is_available": True,
                            "configuration": config,
                            "methodology_framework": config.get("methodology_framework", "UNDERSTAND_ASSESS_EXECUTE_DELIVER"),
                            "specialized_tools": config.get("specialized_tools", []),
                            "compliance_requirements": config.get("compliance_requirements", [])
                        }
                        agent_definitions.append(definition)

                except Exception as e:
                    logger.error(f"Error processing persona {persona_id}: {e}")
                    continue

        return {
            "user_id": user_id,
            "purchased_personas": persona_ids,
            "agent_definitions": agent_definitions,
            "total_count": len(agent_definitions),
            "marketplace_available": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting marketplace personas for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve marketplace personas"
        )


@router.post("/instantiate/{persona_id}")
async def instantiate_marketplace_agent(
    persona_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Instantiate a purchased persona as an active agent."""

    if not LANGGRAPH_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Enhanced marketplace functionality not available"
        )

    try:
        logger.info(f"Instantiating agent for persona {persona_id} for user {current_user.id}")

        # Check if user has purchased this persona
        purchased_personas = db.query(PurchasedItem.persona_id).join(
            Purchase, PurchasedItem.purchase_id == Purchase.id
        ).filter(
            Purchase.user_id == current_user.id,
            Purchase.payment_status == "completed",
            PurchasedItem.persona_id == persona_id
        ).first()

        if not purchased_personas:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Persona not purchased or access denied"
            )

        # Create user context
        try:
            user_context = UserContext(
                user_id=str(current_user.id),
                business_profile=getattr(current_user, 'business_profile', None)
            )
        except NameError:
            # Fallback if UserContext not available
            user_context = type('UserContext', (), {
                'user_id': str(current_user.id),
                'business_profile': getattr(current_user, 'business_profile', None)
            })()

        # Instantiate agent using marketplace factory
        marketplace_factory = get_marketplace_factory()
        if marketplace_factory:
            agent_instance = await marketplace_factory.create_marketplace_agent(
                persona_id, user_context
            )

            return {
                "persona_id": persona_id,
                "agent_id": agent_instance.agent_id,
                "status": "instantiated",
                "capabilities": agent_instance.capabilities if hasattr(agent_instance, 'capabilities') else [],
                "configuration": agent_instance.configuration if hasattr(agent_instance, 'configuration') else {},
                "instantiated_at": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Marketplace agent factory not available"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error instantiating agent for persona {persona_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to instantiate agent"
        )


@router.get("/capabilities/{persona_id}")
async def get_persona_capabilities(
    persona_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get capabilities for a specific persona."""

    try:
        logger.info(f"Getting capabilities for persona {persona_id}")

        # Get persona configuration
        try:
            from agents.langgraph.core.persona_configuration_manager import persona_config_manager
            config = await persona_config_manager.get_persona_configuration(persona_id)
        except ImportError:
            config = None

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Persona configuration not found"
            )

        # Check if user has access (either purchased or free persona)
        is_purchased = False
        if current_user:
            purchased_check = db.query(PurchasedItem.persona_id).join(
                Purchase, PurchasedItem.purchase_id == Purchase.id
            ).filter(
                Purchase.user_id == current_user.id,
                Purchase.payment_status == "completed",
                PurchasedItem.persona_id == persona_id
            ).first()
            is_purchased = bool(purchased_check)

        capabilities_data = {
            "persona_id": persona_id,
            "name": config.get("name", persona_id.replace("_", " ").title()),
            "description": config.get("description", ""),
            "capabilities": config.get("capabilities", []),
            "skills": config.get("skills", []),
            "specialized_tools": config.get("specialized_tools", []),
            "methodology_framework": config.get("methodology_framework", "UNDERSTAND_ASSESS_EXECUTE_DELIVER"),
            "industry_specialization": config.get("industry_specialization"),
            "compliance_requirements": config.get("compliance_requirements", []),
            "performance_optimization": config.get("performance_optimization", {}),
            "is_purchased": is_purchased,
            "pricing_model": config.get("pricing_model", "fixed"),
            "base_price": config.get("base_price", 10.0)
        }

        return capabilities_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting capabilities for persona {persona_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve persona capabilities"
        )


@router.post("/configure/{persona_id}")
async def configure_persona(
    persona_id: str,
    configuration_updates: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Configure a purchased persona with custom settings."""

    if not LANGGRAPH_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Enhanced marketplace functionality not available"
        )

    try:
        logger.info(f"Configuring persona {persona_id} for user {current_user.id}")

        # Check if user has purchased this persona
        purchased_check = db.query(PurchasedItem.persona_id).join(
            Purchase, PurchasedItem.purchase_id == Purchase.id
        ).filter(
            Purchase.user_id == current_user.id,
            Purchase.payment_status == "completed",
            PurchasedItem.persona_id == persona_id
        ).first()

        if not purchased_check:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Persona not purchased or access denied"
            )

        # Update persona configuration
        try:
            from agents.langgraph.core.persona_configuration_manager import persona_config_manager
            success = await persona_config_manager.update_persona_configuration(
                persona_id, configuration_updates, validate=True
            )

            if success:
                updated_config = await persona_config_manager.get_persona_configuration(persona_id)
                return {
                    "persona_id": persona_id,
                    "status": "configured",
                    "configuration": updated_config,
                    "updated_at": datetime.utcnow().isoformat()
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Configuration update failed"
                )

        except ImportError:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Persona configuration manager not available"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error configuring persona {persona_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to configure persona"
        )


@router.post("/workflows/create")
async def create_persona_workflow(
    persona_id: str,
    user_id: str,
    business_profile: Optional[Dict[str, Any]] = None,
    db: Session = Depends(get_db)
):
    """Create optimized workflow for persona and user context."""
    
    if not LANGGRAPH_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Enhanced marketplace functionality not available"
        )
    
    try:
        logger.info(f"Creating persona workflow for {persona_id} and user {user_id}")
        
        # Create user context
        user_context = UserContext(
            user_id=user_id,
            business_profile=business_profile
        )
        
        # Create persona workflow
        workflow = await persona_workflow_builder.create_persona_workflow(
            persona_id, user_context, business_profile
        )
        
        # Store workflow configuration in database
        workflow_config = {
            "persona_id": persona_id,
            "user_id": user_id,
            "business_profile": business_profile,
            "workflow_nodes": len(workflow.nodes) if hasattr(workflow, 'nodes') else 0,
            "created_at": "now"
        }
        
        # Create persona configuration if it doesn't exist
        existing_config = persona_config_service.get_persona_configuration(db, persona_id)
        if not existing_config:
            persona_config_service.create_persona_configuration(
                db=db,
                persona_id=persona_id,
                configuration=workflow_config,
                industry_specialization=business_profile.get("industry") if business_profile else None
            )
        
        return {
            "workflow_id": f"persona_{persona_id}_{user_id}",
            "persona_id": persona_id,
            "user_id": user_id,
            "status": "created",
            "nodes": len(workflow.nodes) if hasattr(workflow, 'nodes') else 0,
            "business_profile_applied": business_profile is not None
        }
        
    except Exception as e:
        logger.error(f"Error creating persona workflow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create persona workflow: {str(e)}"
        )


@router.get("/configurations/{persona_id}")
async def get_persona_configuration(
    persona_id: str,
    db: Session = Depends(get_db)
):
    """Get persona configuration."""
    
    try:
        config = persona_config_service.get_persona_configuration(db, persona_id)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Persona configuration not found for {persona_id}"
            )
        
        return {
            "persona_id": config.persona_id,
            "configuration": config.configuration,
            "industry_specialization": config.industry_specialization,
            "methodology_framework": config.methodology_framework,
            "enable_cross_agent_intelligence": config.enable_cross_agent_intelligence,
            "specialized_tools": config.specialized_tools,
            "compliance_requirements": config.compliance_requirements,
            "workflow_patterns": config.workflow_patterns,
            "performance_optimization": config.performance_optimization,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting persona configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve persona configuration: {str(e)}"
        )


@router.put("/configurations/{persona_id}")
async def update_persona_configuration(
    persona_id: str,
    updates: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Update persona configuration."""
    
    try:
        config = persona_config_service.update_persona_configuration(
            db, persona_id, updates
        )
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Persona configuration not found for {persona_id}"
            )
        
        return {
            "persona_id": config.persona_id,
            "status": "updated",
            "updated_at": config.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating persona configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update persona configuration: {str(e)}"
        )


@router.get("/plugins")
async def get_available_plugins(
    status_filter: Optional[str] = None,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get available agent plugins."""
    
    try:
        if status_filter:
            plugins = agent_plugin_service.get_plugins_by_status(db, status_filter, limit)
        else:
            # Get all plugins (would need a service method for this)
            plugins = agent_plugin_service.get_plugins_by_status(db, "approved", limit)
        
        return {
            "plugins": [
                {
                    "plugin_id": plugin.plugin_id,
                    "name": plugin.name,
                    "version": plugin.version,
                    "author": plugin.author,
                    "description": plugin.description,
                    "status": plugin.status,
                    "supported_industries": plugin.supported_industries,
                    "required_tools": plugin.required_tools,
                    "compliance_certifications": plugin.compliance_certifications,
                    "created_at": plugin.created_at.isoformat()
                }
                for plugin in plugins
            ],
            "total": len(plugins)
        }
        
    except Exception as e:
        logger.error(f"Error getting available plugins: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve plugins: {str(e)}"
        )


@router.get("/adaptations/{user_id}")
async def get_user_workflow_adaptations(
    user_id: int,
    persona_id: Optional[str] = None,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get workflow adaptations for a user."""
    
    try:
        adaptations = workflow_adaptation_service.get_user_adaptations(
            db, user_id, persona_id, limit
        )
        
        return {
            "user_id": user_id,
            "adaptations": [
                {
                    "adaptation_id": adaptation.adaptation_id,
                    "workflow_id": adaptation.workflow_id,
                    "persona_id": adaptation.persona_id,
                    "adaptation_type": adaptation.adaptation_type,
                    "adaptation_triggers": adaptation.adaptation_triggers,
                    "performance_data": adaptation.performance_data,
                    "success_metrics": adaptation.success_metrics,
                    "created_at": adaptation.created_at.isoformat()
                }
                for adaptation in adaptations
            ],
            "total": len(adaptations)
        }
        
    except Exception as e:
        logger.error(f"Error getting workflow adaptations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve workflow adaptations: {str(e)}"
        )
