"""
Enhanced Performance History Manager for Phase 2 Memory Leak Resolution.

This module provides comprehensive performance history management with:
- Automatic cleanup of old performance data
- Configurable retention policies
- Memory-efficient storage for metrics
- Circular buffer implementation for bounded memory usage
"""

import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Deque
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque, defaultdict
import json
import weakref
import gc

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric entry."""
    timestamp: datetime
    metric_name: str
    value: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class RetentionPolicy:
    """Configuration for data retention policies."""
    max_entries: int = 1000
    max_age_hours: int = 24
    cleanup_interval_minutes: int = 30
    compression_enabled: bool = True
    archive_old_data: bool = False


class CircularMetricsBuffer:
    """Memory-efficient circular buffer for metrics storage."""
    
    def __init__(self, max_size: int = 1000):
        """Initialize circular buffer with maximum size."""
        self.max_size = max_size
        self.buffer: Deque[PerformanceMetric] = deque(maxlen=max_size)
        self._total_added = 0
        self._total_dropped = 0
    
    def add_metric(self, metric: PerformanceMetric) -> None:
        """Add metric to buffer, automatically dropping oldest if full."""
        if len(self.buffer) >= self.max_size:
            self._total_dropped += 1
        
        self.buffer.append(metric)
        self._total_added += 1
    
    def get_metrics(self, 
                   since: Optional[datetime] = None,
                   metric_name: Optional[str] = None,
                   limit: Optional[int] = None) -> List[PerformanceMetric]:
        """Get metrics with optional filtering."""
        metrics = list(self.buffer)
        
        # Filter by timestamp
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        # Filter by metric name
        if metric_name:
            metrics = [m for m in metrics if m.metric_name == metric_name]
        
        # Apply limit
        if limit:
            metrics = metrics[-limit:]
        
        return metrics
    
    def get_stats(self) -> Dict[str, Any]:
        """Get buffer statistics."""
        return {
            'current_size': len(self.buffer),
            'max_size': self.max_size,
            'total_added': self._total_added,
            'total_dropped': self._total_dropped,
            'oldest_timestamp': self.buffer[0].timestamp.isoformat() if self.buffer else None,
            'newest_timestamp': self.buffer[-1].timestamp.isoformat() if self.buffer else None
        }
    
    def clear(self) -> int:
        """Clear all metrics and return count of cleared items."""
        count = len(self.buffer)
        self.buffer.clear()
        return count


class EnhancedPerformanceHistoryManager:
    """
    Enhanced performance history manager with automatic cleanup,
    configurable retention policies, and memory-efficient storage.
    """
    
    def __init__(self, retention_policy: Optional[RetentionPolicy] = None):
        """Initialize the performance history manager."""
        self.logger = logging.getLogger(__name__)
        self.retention_policy = retention_policy or RetentionPolicy()
        
        # Metrics storage using circular buffers
        self._metric_buffers: Dict[str, CircularMetricsBuffer] = {}
        self._buffer_weak_refs: Dict[str, weakref.ref] = {}
        
        # Aggregated statistics
        self._aggregated_stats: Dict[str, Dict[str, float]] = defaultdict(dict)
        self._last_aggregation = datetime.now()
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self._is_shutting_down = False
        
        # Performance tracking
        self._operation_counts: Dict[str, int] = defaultdict(int)
        self._memory_usage_history: Deque[float] = deque(maxlen=100)
        
        # Start background tasks
        self._start_background_tasks()
        
        self.logger.info("Enhanced performance history manager initialized")
    
    def _start_background_tasks(self):
        """Start background maintenance tasks."""
        try:
            # Cleanup task
            task = asyncio.create_task(self._cleanup_task())
            self._background_tasks.append(task)
            
            # Aggregation task
            task = asyncio.create_task(self._aggregation_task())
            self._background_tasks.append(task)
            
            # Memory monitoring task
            task = asyncio.create_task(self._memory_monitoring_task())
            self._background_tasks.append(task)
            
            self.logger.info(f"Started {len(self._background_tasks)} performance monitoring tasks")
            
        except Exception as e:
            self.logger.error(f"Failed to start background tasks: {e}")
            raise
    
    async def _cleanup_task(self):
        """Background task for cleaning up old performance data."""
        self.logger.info("Starting performance data cleanup task")
        
        cleanup_interval = self.retention_policy.cleanup_interval_minutes * 60
        
        while not self._is_shutting_down:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=cleanup_interval
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._perform_cleanup()
                    
            except asyncio.CancelledError:
                self.logger.info("Performance cleanup task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in performance cleanup: {e}")
                
        self.logger.info("Performance cleanup task finished")
    
    async def _aggregation_task(self):
        """Background task for aggregating performance statistics."""
        self.logger.info("Starting performance aggregation task")
        
        while not self._is_shutting_down:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=300  # Aggregate every 5 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._perform_aggregation()
                    
            except asyncio.CancelledError:
                self.logger.info("Performance aggregation task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in performance aggregation: {e}")
                
        self.logger.info("Performance aggregation task finished")
    
    async def _memory_monitoring_task(self):
        """Background task for monitoring memory usage."""
        self.logger.info("Starting memory monitoring task")
        
        while not self._is_shutting_down:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=60  # Monitor every minute
                    )
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self._is_shutting_down:
                    await self._monitor_memory_usage()
                    
            except asyncio.CancelledError:
                self.logger.info("Memory monitoring task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in memory monitoring: {e}")
                
        self.logger.info("Memory monitoring task finished")
    
    async def _perform_cleanup(self):
        """Perform cleanup of old performance data."""
        try:
            current_time = datetime.now()
            max_age = timedelta(hours=self.retention_policy.max_age_hours)
            cutoff_time = current_time - max_age
            
            cleaned_metrics = 0
            cleaned_buffers = 0
            
            # Clean up old metrics from buffers
            for buffer_name, buffer in list(self._metric_buffers.items()):
                # Get metrics older than cutoff
                old_metrics = [
                    m for m in buffer.buffer 
                    if m.timestamp < cutoff_time
                ]
                
                if old_metrics:
                    # Remove old metrics by recreating buffer with recent metrics
                    recent_metrics = [
                        m for m in buffer.buffer 
                        if m.timestamp >= cutoff_time
                    ]
                    
                    # Create new buffer and replace
                    new_buffer = CircularMetricsBuffer(buffer.max_size)
                    for metric in recent_metrics:
                        new_buffer.add_metric(metric)
                    
                    self._metric_buffers[buffer_name] = new_buffer
                    cleaned_metrics += len(old_metrics)
            
            # Clean up empty buffers
            empty_buffers = [
                name for name, buffer in self._metric_buffers.items()
                if len(buffer.buffer) == 0
            ]
            
            for buffer_name in empty_buffers:
                del self._metric_buffers[buffer_name]
                if buffer_name in self._buffer_weak_refs:
                    del self._buffer_weak_refs[buffer_name]
                cleaned_buffers += 1
            
            # Clean up weak references
            await self._cleanup_weak_references()
            
            # Force garbage collection
            gc.collect()
            
            if cleaned_metrics > 0 or cleaned_buffers > 0:
                self.logger.info(
                    f"Performance cleanup completed - "
                    f"Cleaned {cleaned_metrics} old metrics, {cleaned_buffers} empty buffers"
                )
                
        except Exception as e:
            self.logger.error(f"Error performing cleanup: {e}")
    
    async def _perform_aggregation(self):
        """Perform aggregation of performance statistics."""
        try:
            current_time = datetime.now()
            
            for buffer_name, buffer in self._metric_buffers.items():
                if not buffer.buffer:
                    continue
                
                # Calculate aggregated statistics
                metrics_by_name = defaultdict(list)
                for metric in buffer.buffer:
                    metrics_by_name[metric.metric_name].append(metric.value)
                
                # Store aggregated stats
                for metric_name, values in metrics_by_name.items():
                    if values:
                        self._aggregated_stats[buffer_name][f"{metric_name}_avg"] = sum(values) / len(values)
                        self._aggregated_stats[buffer_name][f"{metric_name}_min"] = min(values)
                        self._aggregated_stats[buffer_name][f"{metric_name}_max"] = max(values)
                        self._aggregated_stats[buffer_name][f"{metric_name}_count"] = len(values)
            
            self._last_aggregation = current_time
            
        except Exception as e:
            self.logger.error(f"Error performing aggregation: {e}")
    
    async def _monitor_memory_usage(self):
        """Monitor memory usage of the performance manager."""
        try:
            import psutil
            import os
            
            # Get current process memory usage
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            self._memory_usage_history.append(memory_mb)
            
            # Calculate total metrics stored
            total_metrics = sum(len(buffer.buffer) for buffer in self._metric_buffers.values())
            
            # Log memory statistics
            self.logger.debug(
                f"Performance manager memory - "
                f"Process: {memory_mb:.1f}MB, "
                f"Buffers: {len(self._metric_buffers)}, "
                f"Total metrics: {total_metrics}"
            )
            
            # Trigger cleanup if memory usage is high
            if len(self._memory_usage_history) >= 10:
                recent_avg = sum(list(self._memory_usage_history)[-10:]) / 10
                if recent_avg > 500:  # 500MB threshold
                    self.logger.warning(f"High memory usage detected: {recent_avg:.1f}MB")
                    await self._perform_cleanup()
            
        except ImportError:
            # psutil not available, skip memory monitoring
            pass
        except Exception as e:
            self.logger.error(f"Error monitoring memory usage: {e}")
    
    async def _cleanup_weak_references(self):
        """Clean up orphaned weak references."""
        try:
            orphaned_refs = []
            
            for buffer_name, weak_ref in list(self._buffer_weak_refs.items()):
                if weak_ref() is None:
                    orphaned_refs.append(buffer_name)
            
            for buffer_name in orphaned_refs:
                del self._buffer_weak_refs[buffer_name]
            
            if orphaned_refs:
                self.logger.debug(f"Cleaned up {len(orphaned_refs)} orphaned weak references")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up weak references: {e}")

    def record_metric(self,
                     buffer_name: str,
                     metric_name: str,
                     value: float,
                     metadata: Optional[Dict[str, Any]] = None,
                     tags: Optional[Dict[str, str]] = None) -> None:
        """
        Record a performance metric.

        Args:
            buffer_name: Name of the buffer to store the metric
            metric_name: Name of the metric
            value: Metric value
            metadata: Optional metadata
            tags: Optional tags
        """
        try:
            # Get or create buffer
            if buffer_name not in self._metric_buffers:
                self._metric_buffers[buffer_name] = CircularMetricsBuffer(
                    self.retention_policy.max_entries
                )

                # Create weak reference for cleanup
                def cleanup_callback(ref):
                    if buffer_name in self._buffer_weak_refs:
                        del self._buffer_weak_refs[buffer_name]

                buffer = self._metric_buffers[buffer_name]
                self._buffer_weak_refs[buffer_name] = weakref.ref(buffer, cleanup_callback)

            # Create metric
            metric = PerformanceMetric(
                timestamp=datetime.now(),
                metric_name=metric_name,
                value=value,
                metadata=metadata or {},
                tags=tags or {}
            )

            # Add to buffer
            self._metric_buffers[buffer_name].add_metric(metric)
            self._operation_counts['metrics_recorded'] += 1

        except Exception as e:
            self.logger.error(f"Error recording metric {metric_name}: {e}")

    def get_metrics(self,
                   buffer_name: str,
                   since: Optional[datetime] = None,
                   metric_name: Optional[str] = None,
                   limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get performance metrics from a buffer.

        Args:
            buffer_name: Name of the buffer
            since: Only return metrics after this timestamp
            metric_name: Filter by metric name
            limit: Maximum number of metrics to return

        Returns:
            List of metric dictionaries
        """
        try:
            if buffer_name not in self._metric_buffers:
                return []

            buffer = self._metric_buffers[buffer_name]
            metrics = buffer.get_metrics(since, metric_name, limit)

            # Convert to dictionaries
            result = []
            for metric in metrics:
                result.append({
                    'timestamp': metric.timestamp.isoformat(),
                    'metric_name': metric.metric_name,
                    'value': metric.value,
                    'metadata': metric.metadata,
                    'tags': metric.tags
                })

            self._operation_counts['metrics_retrieved'] += 1
            return result

        except Exception as e:
            self.logger.error(f"Error getting metrics from {buffer_name}: {e}")
            return []

    def get_aggregated_stats(self, buffer_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get aggregated performance statistics.

        Args:
            buffer_name: Optional buffer name to filter by

        Returns:
            Dictionary of aggregated statistics
        """
        try:
            if buffer_name:
                return self._aggregated_stats.get(buffer_name, {})
            else:
                return dict(self._aggregated_stats)

        except Exception as e:
            self.logger.error(f"Error getting aggregated stats: {e}")
            return {}

    def get_buffer_stats(self) -> Dict[str, Any]:
        """Get statistics about all metric buffers."""
        try:
            buffer_stats = {}
            total_metrics = 0
            total_dropped = 0

            for buffer_name, buffer in self._metric_buffers.items():
                stats = buffer.get_stats()
                buffer_stats[buffer_name] = stats
                total_metrics += stats['current_size']
                total_dropped += stats['total_dropped']

            return {
                'total_buffers': len(self._metric_buffers),
                'total_metrics': total_metrics,
                'total_dropped': total_dropped,
                'buffer_details': buffer_stats,
                'operation_counts': dict(self._operation_counts),
                'last_aggregation': self._last_aggregation.isoformat(),
                'memory_usage_history': list(self._memory_usage_history)
            }

        except Exception as e:
            self.logger.error(f"Error getting buffer stats: {e}")
            return {}

    def clear_buffer(self, buffer_name: str) -> int:
        """
        Clear all metrics from a specific buffer.

        Args:
            buffer_name: Name of the buffer to clear

        Returns:
            Number of metrics cleared
        """
        try:
            if buffer_name in self._metric_buffers:
                count = self._metric_buffers[buffer_name].clear()
                self._operation_counts['buffers_cleared'] += 1
                return count
            return 0

        except Exception as e:
            self.logger.error(f"Error clearing buffer {buffer_name}: {e}")
            return 0

    def clear_all_buffers(self) -> Dict[str, int]:
        """
        Clear all metric buffers.

        Returns:
            Dictionary mapping buffer names to number of metrics cleared
        """
        try:
            cleared_counts = {}

            for buffer_name in list(self._metric_buffers.keys()):
                cleared_counts[buffer_name] = self.clear_buffer(buffer_name)

            # Clear aggregated stats
            self._aggregated_stats.clear()

            # Force garbage collection
            gc.collect()

            return cleared_counts

        except Exception as e:
            self.logger.error(f"Error clearing all buffers: {e}")
            return {}

    async def shutdown(self, timeout: float = 30.0) -> None:
        """
        Gracefully shutdown the performance history manager.

        Args:
            timeout: Maximum time to wait for tasks to complete
        """
        self.logger.info("Starting performance history manager shutdown...")

        try:
            # Set shutdown flag
            self._is_shutting_down = True
            self._shutdown_event.set()

            # Wait for background tasks to complete
            if self._background_tasks:
                self.logger.info(f"Waiting for {len(self._background_tasks)} background tasks to complete...")

                try:
                    await asyncio.wait_for(
                        asyncio.gather(*self._background_tasks, return_exceptions=True),
                        timeout=timeout
                    )
                    self.logger.info("All background tasks completed gracefully")
                except asyncio.TimeoutError:
                    self.logger.warning(f"Background tasks did not complete within {timeout}s, cancelling...")

                    # Cancel remaining tasks
                    for task in self._background_tasks:
                        if not task.done():
                            task.cancel()

                    # Wait a bit more for cancellation
                    try:
                        await asyncio.wait_for(
                            asyncio.gather(*self._background_tasks, return_exceptions=True),
                            timeout=5.0
                        )
                    except asyncio.TimeoutError:
                        self.logger.error("Some background tasks failed to cancel properly")

            # Clear all data
            cleared_counts = self.clear_all_buffers()

            self.logger.info(f"Performance history manager shutdown completed: {cleared_counts}")

        except Exception as e:
            self.logger.error(f"Error during performance history manager shutdown: {e}")
            raise


# Global performance history manager instance (lazy initialization)
_performance_history_manager: Optional[EnhancedPerformanceHistoryManager] = None


def get_performance_history_manager(
    retention_policy: Optional[RetentionPolicy] = None
) -> EnhancedPerformanceHistoryManager:
    """Get the global performance history manager instance (lazy initialization)."""
    global _performance_history_manager

    if _performance_history_manager is None:
        _performance_history_manager = EnhancedPerformanceHistoryManager(retention_policy)

    return _performance_history_manager


def record_performance_metric(buffer_name: str,
                            metric_name: str,
                            value: float,
                            metadata: Optional[Dict[str, Any]] = None,
                            tags: Optional[Dict[str, str]] = None) -> None:
    """Record a performance metric using the global manager."""
    manager = get_performance_history_manager()
    manager.record_metric(buffer_name, metric_name, value, metadata, tags)
