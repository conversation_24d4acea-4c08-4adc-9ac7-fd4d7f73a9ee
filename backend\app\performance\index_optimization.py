"""
Database index optimization for Phase 3 performance improvements.

This module creates and manages optimized database indexes for:
- Frequently queried columns
- Composite indexes for complex queries
- Partial indexes for filtered queries
- Performance monitoring and analysis
"""

import logging
from typing import Dict, Any, List
from sqlalchemy import text, Index, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

from ..database import engine, get_db
from ..config import DATABASE_URL

logger = logging.getLogger(__name__)


class IndexOptimizer:
    """
    Database index optimizer for improved query performance.
    
    Creates and manages optimized indexes based on query patterns
    and performance analysis.
    """
    
    def __init__(self):
        self.is_postgresql = DATABASE_URL.startswith("postgresql")
        self.created_indexes = []
        self.index_usage_stats = {}
    
    async def create_performance_indexes(self) -> Dict[str, Any]:
        """Create optimized indexes for better query performance."""
        results = {
            "indexes_created": [],
            "errors": []
        }
        
        db = next(get_db())
        try:
            # User table indexes
            await self._create_user_indexes(db, results)
            
            # Conversation table indexes
            await self._create_conversation_indexes(db, results)
            
            # Message table indexes
            await self._create_message_indexes(db, results)
            
            # File table indexes
            await self._create_file_indexes(db, results)
            
            # Task table indexes
            await self._create_task_indexes(db, results)
            
            # Business profile indexes
            await self._create_business_profile_indexes(db, results)
            
            # Persona indexes
            await self._create_persona_indexes(db, results)
            
            # Purchase indexes
            await self._create_purchase_indexes(db, results)
            
            # Cross-table composite indexes
            await self._create_composite_indexes(db, results)
            
            logger.info(f"Created {len(results['indexes_created'])} performance indexes")
            
        except Exception as e:
            logger.error(f"Index creation failed: {e}")
            results["errors"].append(str(e))
        finally:
            db.close()
        
        return results
    
    async def _create_user_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for user table."""
        indexes = [
            # Email lookup (unique constraint already exists, but ensure index)
            ("idx_users_email_active", "users", ["email", "is_active"]),
            
            # Username lookup
            ("idx_users_username_active", "users", ["username", "is_active"]),
            
            # Admin queries
            ("idx_users_superuser_active", "users", ["is_superuser", "is_active"]),
            
            # Date-based queries
            ("idx_users_created_at", "users", ["created_at"]),
            ("idx_users_last_login", "users", ["last_login_at"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_conversation_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for conversation table."""
        indexes = [
            # User conversations lookup
            ("idx_conversations_user_archived", "conversations", ["user_id", "is_archived"]),
            
            # User conversations with ordering
            ("idx_conversations_user_updated", "conversations", ["user_id", "updated_at"]),
            
            # Persona-based queries
            ("idx_conversations_persona_created", "conversations", ["persona_id", "created_at"]),
            
            # Business profile conversations
            ("idx_conversations_profile_updated", "conversations", ["business_profile_id", "updated_at"]),
            
            # Status-based queries
            ("idx_conversations_status_updated", "conversations", ["status", "updated_at"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_message_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for message table."""
        indexes = [
            # Conversation messages with ordering
            ("idx_messages_conv_sequence", "messages", ["conversation_id", "message_sequence"]),
            
            # Thread messages
            ("idx_messages_thread_sequence", "messages", ["thread_id", "thread_sequence"]),
            
            # Parent-child relationships
            ("idx_messages_parent_thread", "messages", ["parent_message_id", "thread_sequence"]),
            
            # Sender-based queries
            ("idx_messages_sender_created", "messages", ["sender", "created_at"]),
            
            # Date-based queries for analytics
            ("idx_messages_created_at", "messages", ["created_at"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
        
        # Full-text search index for PostgreSQL
        if self.is_postgresql:
            await self._create_fulltext_index(db, "idx_messages_content_fts", "messages", "content", results)
    
    async def _create_file_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for file table."""
        indexes = [
            # User files
            ("idx_files_user_created", "files", ["user_id", "created_at"]),
            
            # File type queries
            ("idx_files_user_filename", "files", ["user_id", "filename"]),
            
            # Size-based queries
            ("idx_files_size_created", "files", ["file_size", "created_at"]),
            
            # Processing status
            ("idx_files_processed_created", "files", ["is_processed", "created_at"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_task_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for task table."""
        indexes = [
            # User tasks
            ("idx_tasks_user_created", "tasks", ["user_id", "created_at"]),
            
            # Status-based queries
            ("idx_tasks_status_created", "tasks", ["status", "created_at"]),
            
            # Type-based queries
            ("idx_tasks_type_status", "tasks", ["task_type", "status"]),
            
            # File-related tasks
            ("idx_tasks_input_file", "tasks", ["input_file_id"]),
            
            # Completion tracking
            ("idx_tasks_completed_at", "tasks", ["completed_at"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_business_profile_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for business profile table."""
        indexes = [
            # User profiles
            ("idx_business_profiles_user_active", "business_profiles", ["user_id", "is_active"]),
            
            # Industry-based queries
            ("idx_business_profiles_industry", "business_profiles", ["industry"]),
            
            # Company size queries
            ("idx_business_profiles_size", "business_profiles", ["company_size"]),
            
            # Location-based queries
            ("idx_business_profiles_location", "business_profiles", ["location"]),
            
            # Default profile lookup
            ("idx_business_profiles_default", "business_profiles", ["user_id", "is_default"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_persona_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for persona table."""
        indexes = [
            # Active personas
            ("idx_personas_active_created", "personas", ["is_active", "created_at"]),
            
            # Industry-specific personas
            ("idx_personas_industry_active", "personas", ["industry", "is_active"]),
            
            # Version tracking
            ("idx_persona_versions_persona", "persona_versions", ["persona_id", "version_number"]),
            
            # Current version lookup
            ("idx_persona_versions_current", "persona_versions", ["persona_id", "is_current"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_purchase_indexes(self, db: Session, results: Dict[str, Any]):
        """Create optimized indexes for purchase table."""
        indexes = [
            # User purchases
            ("idx_purchases_user_created", "purchases", ["user_id", "created_at"]),
            
            # Payment status queries
            ("idx_purchases_status_created", "purchases", ["payment_status", "created_at"]),
            
            # Revenue analytics
            ("idx_purchases_status_amount", "purchases", ["payment_status", "total_amount"]),
            
            # Product-based queries
            ("idx_purchases_product_status", "purchases", ["product_type", "payment_status"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_composite_indexes(self, db: Session, results: Dict[str, Any]):
        """Create composite indexes for complex cross-table queries."""
        indexes = [
            # User activity analysis
            ("idx_user_activity_composite", "conversations", 
             ["user_id", "persona_id", "created_at", "status"]),
            
            # Message analytics
            ("idx_message_analytics", "messages", 
             ["conversation_id", "sender", "created_at"]),
            
            # File processing pipeline
            ("idx_file_processing", "files", 
             ["user_id", "is_processed", "file_size", "created_at"]),
            
            # Task execution tracking
            ("idx_task_execution", "tasks", 
             ["user_id", "task_type", "status", "created_at"]),
        ]
        
        for index_name, table_name, columns in indexes:
            await self._create_index_safe(db, index_name, table_name, columns, results)
    
    async def _create_index_safe(self, db: Session, index_name: str, table_name: str, 
                                columns: List[str], results: Dict[str, Any]):
        """Safely create an index, handling existing indexes."""
        try:
            # Check if index already exists
            if self.is_postgresql:
                check_query = text("""
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = :index_name
                """)
                exists = db.execute(check_query, {"index_name": index_name}).fetchone()
            else:
                # SQLite
                check_query = text("""
                    SELECT 1 FROM sqlite_master 
                    WHERE type = 'index' AND name = :index_name
                """)
                exists = db.execute(check_query, {"index_name": index_name}).fetchone()
            
            if exists:
                logger.debug(f"Index {index_name} already exists, skipping")
                return
            
            # Create the index
            columns_str = ", ".join(columns)
            create_query = text(f"""
                CREATE INDEX CONCURRENTLY {index_name} 
                ON {table_name} ({columns_str})
            """)
            
            if not self.is_postgresql:
                # Remove CONCURRENTLY for SQLite
                create_query = text(f"""
                    CREATE INDEX {index_name} 
                    ON {table_name} ({columns_str})
                """)
            
            db.execute(create_query)
            db.commit()
            
            results["indexes_created"].append({
                "name": index_name,
                "table": table_name,
                "columns": columns
            })
            
            self.created_indexes.append(index_name)
            logger.info(f"Created index: {index_name} on {table_name}({columns_str})")
            
        except Exception as e:
            error_msg = f"Failed to create index {index_name}: {str(e)}"
            logger.warning(error_msg)
            results["errors"].append(error_msg)
    
    async def _create_fulltext_index(self, db: Session, index_name: str, table_name: str, 
                                   column: str, results: Dict[str, Any]):
        """Create full-text search index for PostgreSQL."""
        try:
            # Check if index already exists
            check_query = text("""
                SELECT 1 FROM pg_indexes 
                WHERE indexname = :index_name
            """)
            exists = db.execute(check_query, {"index_name": index_name}).fetchone()
            
            if exists:
                logger.debug(f"Full-text index {index_name} already exists, skipping")
                return
            
            # Create GIN index for full-text search
            create_query = text(f"""
                CREATE INDEX CONCURRENTLY {index_name} 
                ON {table_name} USING gin(to_tsvector('english', {column}))
            """)
            
            db.execute(create_query)
            db.commit()
            
            results["indexes_created"].append({
                "name": index_name,
                "table": table_name,
                "type": "fulltext",
                "column": column
            })
            
            logger.info(f"Created full-text index: {index_name} on {table_name}.{column}")
            
        except Exception as e:
            error_msg = f"Failed to create full-text index {index_name}: {str(e)}"
            logger.warning(error_msg)
            results["errors"].append(error_msg)
    
    async def analyze_index_usage(self) -> Dict[str, Any]:
        """Analyze index usage statistics."""
        if not self.is_postgresql:
            return {"message": "Index usage analysis only available for PostgreSQL"}
        
        db = next(get_db())
        try:
            # Get index usage statistics
            usage_query = text("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_tup_read,
                    idx_tup_fetch,
                    idx_scan
                FROM pg_stat_user_indexes
                WHERE schemaname = 'public'
                ORDER BY idx_scan DESC
            """)
            
            results = db.execute(usage_query).fetchall()
            
            usage_stats = []
            for row in results:
                usage_stats.append({
                    "schema": row.schemaname,
                    "table": row.tablename,
                    "index": row.indexname,
                    "tuples_read": row.idx_tup_read,
                    "tuples_fetched": row.idx_tup_fetch,
                    "scans": row.idx_scan
                })
            
            return {
                "index_usage": usage_stats,
                "total_indexes": len(usage_stats),
                "created_indexes": self.created_indexes
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze index usage: {e}")
            return {"error": str(e)}
        finally:
            db.close()


# Global index optimizer instance
index_optimizer = IndexOptimizer()
