#!/usr/bin/env python3
"""
Test script to verify that all marketplace imports work correctly.
"""

import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test all marketplace-related imports."""
    try:
        logger.info("Testing marketplace imports...")
        
        # Test database manager
        logger.info("Testing database manager...")
        from agents.langgraph.core.marketplace_database_manager import marketplace_db_manager
        logger.info("✓ Database manager imported successfully")
        
        # Test persona configuration manager
        logger.info("Testing persona configuration manager...")
        from agents.langgraph.core.persona_configuration_manager import get_persona_config_manager
        persona_manager = get_persona_config_manager()
        logger.info("✓ Persona configuration manager imported successfully")
        
        # Test capability marketplace
        logger.info("Testing capability marketplace...")
        from agents.langgraph.marketplace.capability_marketplace import CapabilityMarketplace
        capability_marketplace = CapabilityMarketplace()
        logger.info("✓ Capability marketplace imported successfully")
        
        # Test marketplace agent factory
        logger.info("Testing marketplace agent factory...")
        from agents.langgraph.core.marketplace_agent_factory import get_marketplace_agent_factory
        agent_factory = get_marketplace_agent_factory()
        logger.info("✓ Marketplace agent factory imported successfully")
        
        # Test purchase-to-agent flow
        logger.info("Testing purchase-to-agent flow...")
        from agents.langgraph.core.purchase_to_agent_flow import get_purchase_to_agent_flow_manager
        flow_manager = get_purchase_to_agent_flow_manager()
        logger.info("✓ Purchase-to-agent flow manager imported successfully")
        
        # Test marketplace event system
        logger.info("Testing marketplace event system...")
        from agents.langgraph.core.marketplace_event_system import get_marketplace_event_system
        event_system = get_marketplace_event_system()
        logger.info("✓ Marketplace event system imported successfully")
        
        # Test enhanced personas API
        logger.info("Testing enhanced personas API...")
        from app.api.enhanced_personas import router
        logger.info("✓ Enhanced personas API imported successfully")
        
        logger.info("✅ All marketplace imports successful!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

def test_lazy_initialization():
    """Test that lazy initialization works correctly."""
    try:
        logger.info("Testing lazy initialization...")
        
        # Test multiple calls return same instance
        from agents.langgraph.core.marketplace_agent_factory import get_marketplace_agent_factory
        factory1 = get_marketplace_agent_factory()
        factory2 = get_marketplace_agent_factory()
        
        if factory1 is factory2:
            logger.info("✓ Lazy initialization working correctly (same instance)")
        else:
            logger.warning("⚠ Lazy initialization may not be working (different instances)")
        
        # Test persona config manager
        from agents.langgraph.core.persona_configuration_manager import get_persona_config_manager
        manager1 = get_persona_config_manager()
        manager2 = get_persona_config_manager()
        
        if manager1 is manager2:
            logger.info("✓ Persona config manager lazy initialization working")
        else:
            logger.warning("⚠ Persona config manager lazy initialization may not be working")
        
        logger.info("✅ Lazy initialization tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Lazy initialization test error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of marketplace components."""
    try:
        logger.info("Testing basic functionality...")
        
        # Test database manager health check
        from agents.langgraph.core.marketplace_database_manager import marketplace_db_manager
        # Note: This is async, so we can't test it here without asyncio
        logger.info("✓ Database manager available")
        
        # Test persona config manager validation
        from agents.langgraph.core.persona_configuration_manager import get_persona_config_manager
        manager = get_persona_config_manager()
        
        # Test validation rules
        validation_rules = manager._load_validation_rules()
        if validation_rules and isinstance(validation_rules, dict):
            logger.info("✓ Persona config manager validation rules loaded")
        else:
            logger.warning("⚠ Persona config manager validation rules not loaded properly")
        
        # Test industry templates
        templates = manager._load_industry_templates()
        if templates and isinstance(templates, dict):
            logger.info("✓ Industry templates loaded")
        else:
            logger.warning("⚠ Industry templates not loaded properly")
        
        logger.info("✅ Basic functionality tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic functionality test error: {e}")
        return False

def main():
    """Main test function."""
    logger.info("Starting marketplace integration tests...")
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test lazy initialization
    if not test_lazy_initialization():
        success = False
    
    # Test basic functionality
    if not test_basic_functionality():
        success = False
    
    if success:
        logger.info("🎉 All tests passed! Marketplace integration is working correctly.")
        sys.exit(0)
    else:
        logger.error("💥 Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
